"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import { Editor } from '@tiptap/react';

interface ImageResizerProps {
  editor: Editor;
  imageNode: any;
  imageElement: HTMLImageElement;
  onResize: (width: number, height: number) => void;
  onResizeEnd: () => void;
}

export function ImageResizer({ 
  editor, 
  imageNode, 
  imageElement, 
  onResize, 
  onResizeEnd 
}: ImageResizerProps) {
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<string | null>(null);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [startSize, setStartSize] = useState({ width: 0, height: 0 });
  const [currentSize, setCurrentSize] = useState({ width: 0, height: 0 });
  const resizerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (imageElement) {
      const rect = imageElement.getBoundingClientRect();
      setCurrentSize({
        width: rect.width,
        height: rect.height,
      });
    }
  }, [imageElement]);

  const handleMouseDown = (e: React.MouseEvent, handle: string) => {
    e.preventDefault();
    e.stopPropagation();

    setIsResizing(true);
    setResizeHandle(handle);
    setStartPos({ x: e.clientX, y: e.clientY });
    setStartSize({ ...currentSize });

    // Add class to prevent text selection
    document.body.classList.add('image-resizing');

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing || !resizeHandle) return;

    const deltaX = e.clientX - startPos.x;
    const deltaY = e.clientY - startPos.y;

    let newWidth = startSize.width;
    let newHeight = startSize.height;

    const aspectRatio = startSize.width / startSize.height;
    const minSize = 50;
    const maxSize = 800;

    // Simplified resize logic - only corner handles for better UX
    switch (resizeHandle) {
      case 'se': // Southeast (bottom-right) - most common
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width + deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 'sw': // Southwest (bottom-left)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width - deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 'ne': // Northeast (top-right)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width + deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 'nw': // Northwest (top-left)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width - deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      default:
        // For edge handles, use proportional resize
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width + deltaX));
        newHeight = newWidth / aspectRatio;
        break;
    }

    setCurrentSize({ width: newWidth, height: newHeight });
    onResize(newWidth, newHeight);
  }, [isResizing, resizeHandle, startPos, startSize, onResize]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    setResizeHandle(null);
    onResizeEnd();

    // Remove class to restore text selection
    document.body.classList.remove('image-resizing');

    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [onResizeEnd, handleMouseMove]);

  if (!imageElement) return null;

  const rect = imageElement.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  return (
    <div
      ref={resizerRef}
      className="absolute pointer-events-none z-50"
      style={{
        top: rect.top + scrollTop,
        left: rect.left + scrollLeft,
        width: currentSize.width,
        height: currentSize.height,
      }}
    >
      {/* Resize Handles */}
      <div className="relative w-full h-full">
        {/* Corner Handles - Primary resize controls */}
        <div
          className="absolute w-4 h-4 bg-blue-500 border-2 border-white rounded cursor-nw-resize pointer-events-auto hover:bg-blue-600 transition-colors shadow-lg"
          style={{ top: -8, left: -8 }}
          onMouseDown={(e) => handleMouseDown(e, 'nw')}
          title="Resize from top-left"
        />
        <div
          className="absolute w-4 h-4 bg-blue-500 border-2 border-white rounded cursor-ne-resize pointer-events-auto hover:bg-blue-600 transition-colors shadow-lg"
          style={{ top: -8, right: -8 }}
          onMouseDown={(e) => handleMouseDown(e, 'ne')}
          title="Resize from top-right"
        />
        <div
          className="absolute w-4 h-4 bg-blue-500 border-2 border-white rounded cursor-sw-resize pointer-events-auto hover:bg-blue-600 transition-colors shadow-lg"
          style={{ bottom: -8, left: -8 }}
          onMouseDown={(e) => handleMouseDown(e, 'sw')}
          title="Resize from bottom-left"
        />
        <div
          className="absolute w-4 h-4 bg-blue-500 border-2 border-white rounded cursor-se-resize pointer-events-auto hover:bg-blue-600 transition-colors shadow-lg"
          style={{ bottom: -8, right: -8 }}
          onMouseDown={(e) => handleMouseDown(e, 'se')}
          title="Resize from bottom-right"
        />

        {/* Selection Border */}
        <div className="absolute inset-0 border-2 border-blue-500 pointer-events-none rounded" />

        {/* Size Display */}
        {isResizing && (
          <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black text-white text-sm px-3 py-1 rounded pointer-events-none shadow-lg">
            {Math.round(currentSize.width)} × {Math.round(currentSize.height)}
          </div>
        )}
      </div>
    </div>
  );
}
