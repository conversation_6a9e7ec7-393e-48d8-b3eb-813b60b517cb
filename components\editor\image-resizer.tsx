"use client";

import { useState, useRef, useEffect } from 'react';
import { Editor } from '@tiptap/react';

interface ImageResizerProps {
  editor: Editor;
  imageNode: any;
  imageElement: HTMLImageElement;
  onResize: (width: number, height: number) => void;
  onResizeEnd: () => void;
}

export function ImageResizer({ 
  editor, 
  imageNode, 
  imageElement, 
  onResize, 
  onResizeEnd 
}: ImageResizerProps) {
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<string | null>(null);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [startSize, setStartSize] = useState({ width: 0, height: 0 });
  const [currentSize, setCurrentSize] = useState({ width: 0, height: 0 });
  const resizerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (imageElement) {
      const rect = imageElement.getBoundingClientRect();
      setCurrentSize({
        width: rect.width,
        height: rect.height,
      });
    }
  }, [imageElement]);

  const handleMouseDown = (e: React.MouseEvent, handle: string) => {
    e.preventDefault();
    e.stopPropagation();

    setIsResizing(true);
    setResizeHandle(handle);
    setStartPos({ x: e.clientX, y: e.clientY });
    setStartSize({ ...currentSize });

    // Add class to prevent text selection
    document.body.classList.add('image-resizing');

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing || !resizeHandle) return;

    const deltaX = e.clientX - startPos.x;
    const deltaY = e.clientY - startPos.y;
    
    let newWidth = startSize.width;
    let newHeight = startSize.height;
    
    const aspectRatio = startSize.width / startSize.height;
    const minSize = 50;
    const maxSize = 800;

    switch (resizeHandle) {
      case 'se': // Southeast (bottom-right)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width + deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 'sw': // Southwest (bottom-left)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width - deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 'ne': // Northeast (top-right)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width + deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 'nw': // Northwest (top-left)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width - deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 'e': // East (right)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width + deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 'w': // West (left)
        newWidth = Math.max(minSize, Math.min(maxSize, startSize.width - deltaX));
        newHeight = newWidth / aspectRatio;
        break;
      case 's': // South (bottom)
        newHeight = Math.max(minSize / aspectRatio, Math.min(maxSize / aspectRatio, startSize.height + deltaY));
        newWidth = newHeight * aspectRatio;
        break;
      case 'n': // North (top)
        newHeight = Math.max(minSize / aspectRatio, Math.min(maxSize / aspectRatio, startSize.height - deltaY));
        newWidth = newHeight * aspectRatio;
        break;
    }

    setCurrentSize({ width: newWidth, height: newHeight });
    onResize(newWidth, newHeight);
  };

  const handleMouseUp = () => {
    setIsResizing(false);
    setResizeHandle(null);
    onResizeEnd();

    // Remove class to restore text selection
    document.body.classList.remove('image-resizing');

    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  if (!imageElement) return null;

  const rect = imageElement.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  return (
    <div
      ref={resizerRef}
      className="absolute pointer-events-none z-50"
      style={{
        top: rect.top + scrollTop,
        left: rect.left + scrollLeft,
        width: currentSize.width,
        height: currentSize.height,
      }}
    >
      {/* Resize Handles */}
      <div className="relative w-full h-full">
        {/* Corner Handles */}
        <div
          className="absolute w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-nw-resize pointer-events-auto"
          style={{ top: -6, left: -6 }}
          onMouseDown={(e) => handleMouseDown(e, 'nw')}
        />
        <div
          className="absolute w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-ne-resize pointer-events-auto"
          style={{ top: -6, right: -6 }}
          onMouseDown={(e) => handleMouseDown(e, 'ne')}
        />
        <div
          className="absolute w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-sw-resize pointer-events-auto"
          style={{ bottom: -6, left: -6 }}
          onMouseDown={(e) => handleMouseDown(e, 'sw')}
        />
        <div
          className="absolute w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-se-resize pointer-events-auto"
          style={{ bottom: -6, right: -6 }}
          onMouseDown={(e) => handleMouseDown(e, 'se')}
        />

        {/* Edge Handles */}
        <div
          className="absolute w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-n-resize pointer-events-auto"
          style={{ top: -6, left: '50%', transform: 'translateX(-50%)' }}
          onMouseDown={(e) => handleMouseDown(e, 'n')}
        />
        <div
          className="absolute w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-s-resize pointer-events-auto"
          style={{ bottom: -6, left: '50%', transform: 'translateX(-50%)' }}
          onMouseDown={(e) => handleMouseDown(e, 's')}
        />
        <div
          className="absolute w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-w-resize pointer-events-auto"
          style={{ top: '50%', left: -6, transform: 'translateY(-50%)' }}
          onMouseDown={(e) => handleMouseDown(e, 'w')}
        />
        <div
          className="absolute w-3 h-3 bg-blue-500 border border-white rounded-sm cursor-e-resize pointer-events-auto"
          style={{ top: '50%', right: -6, transform: 'translateY(-50%)' }}
          onMouseDown={(e) => handleMouseDown(e, 'e')}
        />

        {/* Selection Border */}
        <div className="absolute inset-0 border-2 border-blue-500 pointer-events-none" />
        
        {/* Size Display */}
        {isResizing && (
          <div className="absolute -top-8 left-0 bg-black text-white text-xs px-2 py-1 rounded pointer-events-none">
            {Math.round(currentSize.width)} × {Math.round(currentSize.height)}
          </div>
        )}
      </div>
    </div>
  );
}
