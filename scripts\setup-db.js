#!/usr/bin/env node

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function setupDatabase() {
  console.log('🚀 Setting up database...');

  try {
    console.log('📝 Generating database schema...');
    execSync('npm run db:generate', { stdio: 'inherit' });

    console.log('📊 Running database migration...');
    execSync('npm run migrate', { stdio: 'inherit' });

    console.log('');
    const seedChoice = await askQuestion('Do you want to seed the database with sample data? (y/N): ');

    if (seedChoice.toLowerCase() === 'y' || seedChoice.toLowerCase() === 'yes') {
      console.log('');
      const advancedChoice = await askQuestion('Use advanced seeding (more articles)? (y/N): ');

      if (advancedChoice.toLowerCase() === 'y' || advancedChoice.toLowerCase() === 'yes') {
        console.log('🌱 Running advanced seeding...');
        execSync('npm run seed:advanced', { stdio: 'inherit' });
      } else {
        console.log('🌱 Running basic seeding...');
        execSync('npm run seed', { stdio: 'inherit' });
      }
    }

    console.log('');
    console.log('✅ Database setup complete!');
    console.log('');
    console.log('🎉 Your CMS is ready to use!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Open your browser: http://localhost:3000');
    console.log('3. Login with admin credentials (check seeder output above)');
    console.log('');
    console.log('Available commands:');
    console.log('• npm run dev          - Start development server');
    console.log('• npm run db:studio    - Open Drizzle Studio');
    console.log('• npm run seed         - Reseed with basic data');
    console.log('• npm run seed:advanced - Reseed with advanced data');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

setupDatabase();
