#!/usr/bin/env node

const { execSync } = require('child_process');

console.log('🚀 Setting up database...');

try {
  console.log('📝 Generating database schema...');
  execSync('npm run db:generate', { stdio: 'inherit' });
  
  console.log('📊 Pushing schema to database...');
  execSync('npm run db:push', { stdio: 'inherit' });
  
  console.log('✅ Database setup complete!');
  console.log('');
  console.log('Next steps:');
  console.log('1. Start the development server: npm run dev');
  console.log('2. Create admin user: curl -X POST http://localhost:3000/api/init');
  console.log('3. Login at: http://localhost:3000/login');
} catch (error) {
  console.error('❌ Database setup failed:', error.message);
  process.exit(1);
}
