#!/usr/bin/env node

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const { createId } = require('@paralleldrive/cuid2');

// Sample data
const sampleUsers = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'editor123',
    name: 'Editor User',
    role: 'user'
  }
];

const sampleArticles = [
  {
    title: 'Welcome to Our CMS',
    content: `# Welcome to Our Content Management System

This is your first article! Our CMS is built with modern technologies including:

- **Next.js 15** for the frontend framework
- **MySQL** for reliable data storage
- **Drizzle ORM** for type-safe database operations
- **ShadCN UI** for beautiful, accessible components
- **Tailwind CSS** for responsive styling

## Getting Started

You can start creating amazing content right away. The admin dashboard provides all the tools you need to:

1. Create and edit articles
2. Upload images
3. Manage publication status
4. Organize your content

## Features

Our CMS includes many powerful features:

- **Rich Text Editing**: Write content with ease
- **Image Management**: Upload and manage your media
- **SEO Friendly**: Automatic slug generation and meta tags
- **Responsive Design**: Looks great on all devices
- **Search Functionality**: Find content quickly

Start exploring and create something amazing!`,
    excerpt: 'Learn about the features and capabilities of our modern CMS built with Next.js, MySQL, and cutting-edge technologies.',
    coverImage: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop',
    status: 'published'
  },
  {
    title: 'Building Modern Web Applications',
    content: `# The Future of Web Development

Web development has evolved tremendously over the past few years. Modern frameworks and tools have made it easier than ever to build fast, scalable, and maintainable applications.

## Key Technologies

### Frontend Frameworks
- **React**: Component-based architecture
- **Next.js**: Full-stack React framework
- **Vue.js**: Progressive framework
- **Svelte**: Compile-time optimizations

### Backend Solutions
- **Node.js**: JavaScript runtime
- **Deno**: Secure runtime for JavaScript and TypeScript
- **Bun**: Fast all-in-one JavaScript runtime

### Databases
- **PostgreSQL**: Advanced relational database
- **MySQL**: Reliable and widely-used
- **MongoDB**: Document-based NoSQL
- **Redis**: In-memory data structure store

## Best Practices

1. **Type Safety**: Use TypeScript for better development experience
2. **Testing**: Implement comprehensive test coverage
3. **Performance**: Optimize for speed and user experience
4. **Security**: Follow security best practices
5. **Accessibility**: Make your apps usable by everyone

The web development landscape continues to evolve, and staying up-to-date with the latest trends and technologies is crucial for building successful applications.`,
    excerpt: 'Explore the latest trends and technologies in modern web development, from frontend frameworks to backend solutions.',
    coverImage: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop',
    status: 'published'
  },
  {
    title: 'The Art of Content Creation',
    content: `# Creating Engaging Content

Content is king in the digital world. Whether you're writing blog posts, creating videos, or designing graphics, the quality of your content determines your success.

## Understanding Your Audience

Before creating any content, it's essential to understand who you're creating it for:

- **Demographics**: Age, location, interests
- **Pain Points**: What problems do they face?
- **Preferences**: How do they consume content?
- **Goals**: What are they trying to achieve?

## Content Types

### Written Content
- Blog posts and articles
- Social media posts
- Email newsletters
- Case studies and whitepapers

### Visual Content
- Infographics and charts
- Photos and illustrations
- Videos and animations
- Interactive content

### Audio Content
- Podcasts
- Audio books
- Voice-overs
- Music and sound effects

## Content Strategy

A successful content strategy includes:

1. **Planning**: Create a content calendar
2. **Creation**: Develop high-quality content
3. **Distribution**: Share across multiple channels
4. **Engagement**: Interact with your audience
5. **Analysis**: Measure performance and iterate

Remember, consistency is key. Regular, high-quality content builds trust and keeps your audience engaged.`,
    excerpt: 'Master the art of creating engaging content that resonates with your audience and drives meaningful engagement.',
    coverImage: 'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=800&h=400&fit=crop',
    status: 'published'
  },
  {
    title: 'Database Design Best Practices',
    content: `# Designing Efficient Databases

A well-designed database is the foundation of any successful application. Poor database design can lead to performance issues, data inconsistencies, and maintenance nightmares.

## Fundamental Principles

### Normalization
- **First Normal Form (1NF)**: Eliminate repeating groups
- **Second Normal Form (2NF)**: Remove partial dependencies
- **Third Normal Form (3NF)**: Eliminate transitive dependencies

### Denormalization
Sometimes, strategic denormalization can improve performance:
- Read-heavy applications
- Reporting and analytics
- Caching frequently accessed data

## Schema Design

### Tables and Relationships
- **One-to-One**: User profiles
- **One-to-Many**: Users and articles
- **Many-to-Many**: Articles and tags

### Indexing Strategy
- Primary keys for unique identification
- Foreign keys for referential integrity
- Composite indexes for complex queries
- Partial indexes for filtered data

## Performance Optimization

### Query Optimization
- Use appropriate indexes
- Avoid N+1 queries
- Implement query caching
- Monitor slow queries

### Connection Management
- Connection pooling
- Connection limits
- Timeout configurations
- Load balancing

## Security Considerations

- Input validation and sanitization
- Parameterized queries to prevent SQL injection
- Role-based access control
- Data encryption at rest and in transit

A well-designed database scales with your application and provides a solid foundation for growth.`,
    excerpt: 'Learn essential database design principles and best practices for building scalable, efficient data storage solutions.',
    coverImage: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=400&fit=crop',
    status: 'published'
  },
  {
    title: 'Draft: Upcoming Features',
    content: `# Exciting New Features Coming Soon

We're constantly working to improve our CMS platform. Here's a sneak peek at some exciting features we're developing:

## Enhanced Editor
- Rich text formatting options
- Drag-and-drop image insertion
- Code syntax highlighting
- Real-time collaboration

## Advanced Media Management
- Image optimization and compression
- Video upload and streaming
- Gallery management
- CDN integration

## SEO Improvements
- Meta tag management
- Sitemap generation
- Schema markup
- Performance optimization

## User Management
- Role-based permissions
- User activity tracking
- Team collaboration tools
- Guest author support

Stay tuned for these amazing updates!`,
    excerpt: 'Get a preview of the exciting new features and improvements coming to our CMS platform.',
    coverImage: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=400&fit=crop',
    status: 'draft'
  }
];

function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
}

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');
  
  try {
    // Parse DATABASE_URL
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl) {
      throw new Error('DATABASE_URL environment variable is required');
    }
    
    const url = new URL(dbUrl);
    
    const connection = await mysql.createConnection({
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      user: url.username,
      password: url.password,
      database: url.pathname.slice(1),
    });
    
    console.log('✅ Connected to database');
    
    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await connection.execute('DELETE FROM articles');
    await connection.execute('DELETE FROM users');
    
    // Seed users
    console.log('👥 Seeding users...');
    const userIds = [];
    
    for (const user of sampleUsers) {
      const userId = createId();
      const hashedPassword = await bcrypt.hash(user.password, 12);
      
      await connection.execute(
        'INSERT INTO users (id, email, password, name, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
        [userId, user.email, hashedPassword, user.name, user.role]
      );
      
      userIds.push(userId);
      console.log(`   ✓ Created user: ${user.email}`);
    }
    
    // Seed articles
    console.log('📝 Seeding articles...');
    
    for (let i = 0; i < sampleArticles.length; i++) {
      const article = sampleArticles[i];
      const articleId = createId();
      const slug = generateSlug(article.title);
      const authorId = userIds[0]; // Use admin user as author
      const publishedAt = article.status === 'published' ? new Date() : null;
      
      await connection.execute(
        'INSERT INTO articles (id, title, slug, content, excerpt, cover_image, status, author_id, published_at, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
        [articleId, article.title, slug, article.content, article.excerpt, article.coverImage, article.status, authorId, publishedAt]
      );
      
      console.log(`   ✓ Created article: ${article.title}`);
    }
    
    await connection.end();
    
    console.log('🎉 Database seeding completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log(`   • ${sampleUsers.length} users created`);
    console.log(`   • ${sampleArticles.length} articles created`);
    console.log('');
    console.log('🔑 Login credentials:');
    console.log('   Admin: <EMAIL> / admin123');
    console.log('   Editor: <EMAIL> / editor123');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
    process.exit(1);
  }
}

seedDatabase();
