{"name": "cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:create": "node scripts/create-database.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "seed:advanced": "node scripts/seed-advanced.js", "setup": "node scripts/setup-db.js"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@hookform/resolvers": "^5.1.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "lucide-react": "^0.525.0", "mysql2": "^3.14.1", "next": "15.3.4", "next-auth": "^4.24.7", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.59.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}