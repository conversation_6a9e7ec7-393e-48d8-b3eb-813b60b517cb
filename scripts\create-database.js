#!/usr/bin/env node

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const mysql = require('mysql2/promise');

async function createDatabase() {
  console.log('🗄️  Creating database...');
  
  try {
    // Parse DATABASE_URL
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl) {
      throw new Error('DATABASE_URL environment variable is required');
    }
    
    const url = new URL(dbUrl);
    const databaseName = url.pathname.slice(1); // Remove leading slash
    
    // Connect without specifying database
    const connection = await mysql.createConnection({
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      user: url.username,
      password: url.password,
    });
    
    console.log('✅ Connected to MySQL server');
    
    // Create database if it doesn't exist
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${databaseName}\``);
    console.log(`✅ Database '${databaseName}' created or already exists`);
    
    await connection.end();
    
    console.log('🎉 Database creation completed!');
    
  } catch (error) {
    console.error('❌ Database creation failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('');
      console.error('💡 Make sure MySQL server is running:');
      console.error('   • Windows: Start MySQL service');
      console.error('   • macOS: brew services start mysql');
      console.error('   • Linux: sudo systemctl start mysql');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('');
      console.error('💡 Check your database credentials in .env.local');
      console.error('   • Username and password must be correct');
      console.error('   • User must have CREATE DATABASE privileges');
    }
    
    process.exit(1);
  }
}

createDatabase();
