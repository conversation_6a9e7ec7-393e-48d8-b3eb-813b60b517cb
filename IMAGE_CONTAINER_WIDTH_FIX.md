# Image Container Width Fix

## Problem
The `.image-wrapper` container was always full width regardless of the actual image size selected by the user. This made the layout look unbalanced, especially for smaller images.

## Solution
Modified the image wrapper styling and rendering logic to:

1. **CSS Changes (globals.css):**
   - Changed `.image-wrapper` from `display: flex` to `display: inline-flex`
   - Added `width: fit-content` and `max-width: 100%` to make container size match image
   - Updated alignment classes to properly center, left, or right align the containers
   - Added responsive behavior for mobile devices

2. **Extension Changes (custom-image.ts):**
   - Added wrapper style calculation based on image dimensions
   - Set wrapper width to match image width for specific pixel dimensions
   - Maintained 100% width for responsive layouts

## Key Improvements

### Container Sizing
- Containers now automatically adjust to image width
- Small images (300px) get small containers
- Large images (800px) get appropriately sized containers
- Responsive images (100%) still use full width

### Alignment Behavior
- **Left align**: Container floats left with proper margins
- **Center align**: Container centers in the content area
- **Right align**: Container floats right with proper margins

### Responsive Design
- On mobile (< 768px), all containers become full width and centered
- Images scale appropriately within their containers
- Maintains readability and usability on small screens

## CSS Classes Updated
- `.ProseMirror .image-wrapper` - Main container styling
- `.ProseMirror .image-wrapper.align-*` - Alignment specific rules
- `.ProseMirror .custom-image` - Image element styling
- Responsive rules for mobile devices

## Technical Details
The solution uses a combination of:
- `display: inline-flex` for automatic width fitting
- `width: fit-content` for container size matching
- Dynamic inline styles for specific dimensions
- CSS transforms for proper centering
- Media queries for responsive behavior

This ensures that the container visually "wraps" around the image size chosen by the user, creating a more professional and balanced layout.
