"use client";

import { useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import TableCell from '@tiptap/extension-table-cell';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { Color } from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';
import Highlight from '@tiptap/extension-highlight';
import Typography from '@tiptap/extension-typography';
import Placeholder from '@tiptap/extension-placeholder';
import { createLowlight, common } from 'lowlight';

import { EditorToolbar } from './editor-toolbar';
import { ImageToolbar } from './image-toolbar';
import { CustomImage } from './extensions/custom-image';
import { cn } from '@/lib/utils';

// Create lowlight instance with common languages
const lowlight = createLowlight(common);

interface GutenbergEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

interface ImageToolbarState {
  show: boolean;
  node: any;
  position: { top: number; left: number };
}

export function GutenbergEditor({
  content,
  onChange,
  placeholder = "Start writing your content...",
  className
}: GutenbergEditorProps) {
  const [imageToolbar, setImageToolbar] = useState<ImageToolbarState>({
    show: false,
    node: null,
    position: { top: 0, left: 0 },
  });
  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      CustomImage.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: 'bg-gray-100 rounded-md p-4 font-mono text-sm',
        },
      }),
      Color.configure({ types: [TextStyle.name] }),
      TextStyle,
      Highlight.configure({
        multicolor: true,
      }),
      Typography,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
          'min-h-[300px] p-4',
          className
        ),
      },
      handleClick: (view, pos, event) => {
        const { state } = view;
        const node = state.doc.nodeAt(pos);

        if (node && node.type.name === 'customImage') {
          const target = event.target as HTMLElement;
          const rect = target.getBoundingClientRect();

          setImageToolbar({
            show: true,
            node,
            position: {
              top: rect.top + window.scrollY,
              left: rect.left + rect.width / 2,
            },
          });

          return true;
        } else {
          setImageToolbar(prev => ({ ...prev, show: false }));
        }

        return false;
      },
    },
  });

  if (!editor) {
    return null;
  }

  return (
    <div className="border rounded-lg overflow-hidden relative">
      <EditorToolbar editor={editor} />
      <div className="bg-white">
        <EditorContent editor={editor} />
      </div>

      {/* Image Toolbar */}
      {imageToolbar.show && (
        <ImageToolbar
          editor={editor}
          imageNode={imageToolbar.node}
          position={imageToolbar.position}
          onClose={() => setImageToolbar(prev => ({ ...prev, show: false }))}
        />
      )}
    </div>
  );
}
