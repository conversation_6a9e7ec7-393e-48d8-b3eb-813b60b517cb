"use client";

import { useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import TableCell from '@tiptap/extension-table-cell';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { Color } from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';
import Highlight from '@tiptap/extension-highlight';
import Typography from '@tiptap/extension-typography';
import Placeholder from '@tiptap/extension-placeholder';
import { createLowlight, common } from 'lowlight';
import { Node } from '@tiptap/pm/model';

import { EditorToolbar } from './editor-toolbar';
import { ImageToolbar } from './image-toolbar';
import { SimpleImageResizer } from './simple-image-resizer';
import { CustomImage } from './extensions/custom-image';
import { cn } from '@/lib/utils';

// Create lowlight instance with common languages
const lowlight = createLowlight(common);

interface GutenbergEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

interface ImageToolbarState {
  show: boolean;
  node: Node | null;
  position: { top: number; left: number };
}

interface ImageResizerState {
  show: boolean;
  node: Node | null;
  element: HTMLImageElement | null;
}

export function GutenbergEditor({
  content,
  onChange,
  placeholder = "Start writing your content...",
  className
}: GutenbergEditorProps) {
  const [imageToolbar, setImageToolbar] = useState<ImageToolbarState>({
    show: false,
    node: null,
    position: { top: 0, left: 0 },
  });

  const [imageResizer, setImageResizer] = useState<ImageResizerState>({
    show: false,
    node: null,
    element: null,
  });
  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      CustomImage.configure({
        HTMLAttributes: {
          class: 'custom-image',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: 'bg-gray-100 rounded-md p-4 font-mono text-sm',
        },
      }),
      Color.configure({ types: [TextStyle.name] }),
      TextStyle,
      Highlight.configure({
        multicolor: true,
      }),
      Typography,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-lg max-w-none mx-auto focus:outline-none',
          'min-h-[300px] p-6',
          'prose-headings:font-bold prose-headings:text-gray-900',
          'prose-p:text-gray-700 prose-p:leading-relaxed',
          'prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline',
          'prose-strong:text-gray-900 prose-strong:font-semibold',
          'prose-blockquote:border-l-4 prose-blockquote:border-blue-500 prose-blockquote:pl-4 prose-blockquote:italic',
          'prose-code:bg-gray-100 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm',
          'prose-pre:bg-gray-900 prose-pre:text-gray-100',
          className
        ),
      },
      handleClick: (view, pos, event) => {
        const { state } = view;
        const node = state.doc.nodeAt(pos);

        if (node && node.type.name === 'customImage') {
          const target = event.target as HTMLElement;
          
          // Find the actual img element
          const imgElement = target.tagName === 'IMG' ? target as HTMLImageElement :
                           target.querySelector('img') as HTMLImageElement;

          if (imgElement) {
            const rect = imgElement.getBoundingClientRect();
            
            setImageToolbar({
              show: true,
              node,
              position: {
                top: rect.top + window.scrollY - 50,
                left: rect.left + rect.width / 2,
              },
            });

            setImageResizer({
              show: true,
              node,
              element: imgElement,
            });
          }

          return true;
        } else {
          setImageToolbar(prev => ({ ...prev, show: false }));
          setImageResizer(prev => ({ ...prev, show: false }));
        }

        return false;
      },
    },
  });

  if (!editor) {
    return null;
  }

  return (
    <div 
      className="border rounded-xl overflow-hidden relative bg-white shadow-sm"
      onClick={(e) => {
        // Only handle clicks on the editor content, not on toolbars
        if ((e.target as HTMLElement).closest('.fixed')) {
          e.stopPropagation();
        }
      }}
    >
      <EditorToolbar editor={editor} />
      <div className="bg-white border-t">
        <EditorContent editor={editor} />
      </div>

      {/* Image Toolbar */}
      {imageToolbar.show && (
        <ImageToolbar
          editor={editor}
          imageNode={imageToolbar.node}
          position={imageToolbar.position}
          onClose={() => {
            setImageToolbar(prev => ({ ...prev, show: false }));
            setImageResizer(prev => ({ ...prev, show: false }));
          }}
        />
      )}

      {/* Image Resizer - moved back outside to use fixed positioning */}
      {imageResizer.show && imageResizer.element && editor && (
        <SimpleImageResizer
          imageElement={imageResizer.element}
          onResize={(width, height) => {
            // Validate input values to prevent NaN or invalid values
            if (!isFinite(width) || !isFinite(height) || width <= 0 || height <= 0) {
              console.warn('Invalid resize dimensions received:', { width, height });
              return;
            }

            // Update node immediately with proper attribute format
            const { state } = editor;
            const { doc } = state;
            const imageSrc = imageResizer.node?.attrs?.src;

            if (!imageSrc) return;

            let found = false;
            const tr = state.tr;

            doc.descendants((node: Node, pos: number) => {
              if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
                tr.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  width: Math.round(width).toString(),
                  height: Math.round(height).toString(),
                });
                found = true;
                return false;
              }
            });

            if (found) {
              editor.view.dispatch(tr);
            }
          }}
        />
      )}
    </div>
  );
}
