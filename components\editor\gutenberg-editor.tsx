"use client";

import { useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import TableCell from '@tiptap/extension-table-cell';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { Color } from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';
import Highlight from '@tiptap/extension-highlight';
import Typography from '@tiptap/extension-typography';
import Placeholder from '@tiptap/extension-placeholder';
import { createLowlight, common } from 'lowlight';

import { EditorToolbar } from './editor-toolbar';
import { ImageToolbar } from './image-toolbar';
import { SimpleImageResizer } from './simple-image-resizer';
import { CustomImage } from './extensions/custom-image';
import { cn } from '@/lib/utils';

// Create lowlight instance with common languages
const lowlight = createLowlight(common);

interface GutenbergEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

interface ImageToolbarState {
  show: boolean;
  node: any;
  position: { top: number; left: number };
}

interface ImageResizerState {
  show: boolean;
  node: any;
  element: HTMLImageElement | null;
}

export function GutenbergEditor({
  content,
  onChange,
  placeholder = "Start writing your content...",
  className
}: GutenbergEditorProps) {
  const [imageToolbar, setImageToolbar] = useState<ImageToolbarState>({
    show: false,
    node: null,
    position: { top: 0, left: 0 },
  });

  const [imageResizer, setImageResizer] = useState<ImageResizerState>({
    show: false,
    node: null,
    element: null,
  });
  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      CustomImage.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: 'bg-gray-100 rounded-md p-4 font-mono text-sm',
        },
      }),
      Color.configure({ types: [TextStyle.name] }),
      TextStyle,
      Highlight.configure({
        multicolor: true,
      }),
      Typography,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
          'min-h-[300px] p-4',
          className
        ),
      },
      handleClick: (view, pos, event) => {
        const { state } = view;
        const node = state.doc.nodeAt(pos);

        if (node && node.type.name === 'customImage') {
          const target = event.target as HTMLElement;
          const rect = target.getBoundingClientRect();

          // Find the actual img element
          const imgElement = target.tagName === 'IMG' ? target as HTMLImageElement :
                           target.querySelector('img') as HTMLImageElement;

          console.log('Image clicked:', { node, imgElement, rect });

          // Simple click to show resize handles

          setImageToolbar({
            show: true,
            node,
            position: {
              top: rect.top + window.scrollY,
              left: rect.left + rect.width / 2,
            },
          });

          setImageResizer({
            show: true,
            node,
            element: imgElement,
          });

          return true;
        } else {
          setImageToolbar(prev => ({ ...prev, show: false }));
          setImageResizer(prev => ({ ...prev, show: false }));
        }

        return false;
      },
    },
  });

  const handleImageResize = (width: number, height: number) => {
    console.log('Resizing image:', { width, height });
    if (imageResizer.element) {
      // Apply temporary visual resize
      imageResizer.element.style.width = `${width}px`;
      imageResizer.element.style.height = `${height}px`;
    }
  };

  const handleImageResizeEnd = () => {
    console.log('Resize ended');
    if (imageResizer.element && imageResizer.node) {
      const width = imageResizer.element.style.width;
      const height = imageResizer.element.style.height;

      console.log('Final size:', { width, height });

      // Update the node attributes
      const { state } = editor;
      const { doc } = state;
      const imageSrc = imageResizer.node?.attrs?.src;

      if (!imageSrc) {
        console.log('No image src found');
        return;
      }

      let found = false;
      const tr = state.tr;

      doc.descendants((node, pos) => {
        if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
          console.log('Updating node at position:', pos);
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            width: width.replace('px', ''),
            height: height.replace('px', ''),
          });
          found = true;
          return false;
        }
      });

      if (found) {
        console.log('Dispatching transaction');
        editor.view.dispatch(tr);
      } else {
        console.log('Image node not found for update');
      }
    }
  };

  if (!editor) {
    return null;
  }

  return (
    <div className="border rounded-lg overflow-hidden relative">
      <EditorToolbar editor={editor} />
      <div className="bg-white">
        <EditorContent editor={editor} />
      </div>

      {/* Image Toolbar */}
      {imageToolbar.show && (
        <ImageToolbar
          editor={editor}
          imageNode={imageToolbar.node}
          position={imageToolbar.position}
          onClose={() => {
            setImageToolbar(prev => ({ ...prev, show: false }));
            setImageResizer(prev => ({ ...prev, show: false }));
          }}
        />
      )}

      {/* Image Resizer */}
      {imageResizer.show && imageResizer.element && (
        <SimpleImageResizer
          imageElement={imageResizer.element}
          onResize={(width, height) => {
            console.log('Simple resize:', { width, height });
            // Update node immediately
            const { state } = editor;
            const { doc } = state;
            const imageSrc = imageResizer.node?.attrs?.src;

            if (!imageSrc) return;

            let found = false;
            const tr = state.tr;

            doc.descendants((node, pos) => {
              if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
                tr.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  width: Math.round(width).toString(),
                  height: Math.round(height).toString(),
                });
                found = true;
                return false;
              }
            });

            if (found) {
              editor.view.dispatch(tr);
            }
          }}
        />
      )}
    </div>
  );
}
