import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db, articles } from "@/db";
import { eq } from "drizzle-orm";
import { generateSlug } from "@/lib/utils";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const article = await db
      .select()
      .from(articles)
      .where(eq(articles.id, params.id))
      .limit(1);

    if (!article.length) {
      return NextResponse.json(
        { error: "Article not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(article[0]);
  } catch (error) {
    console.error("Error fetching article:", error);
    return NextResponse.json(
      { error: "Failed to fetch article" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { title, content, excerpt, coverImage, status } = body;

    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      );
    }

    // Check if article exists
    const existingArticle = await db
      .select()
      .from(articles)
      .where(eq(articles.id, params.id))
      .limit(1);

    if (!existingArticle.length) {
      return NextResponse.json(
        { error: "Article not found" },
        { status: 404 }
      );
    }

    const slug = generateSlug(title);
    
    // Check if slug already exists (excluding current article)
    const slugCheck = await db
      .select()
      .from(articles)
      .where(eq(articles.slug, slug))
      .limit(1);

    if (slugCheck.length > 0 && slugCheck[0].id !== params.id) {
      return NextResponse.json(
        { error: "Article with this title already exists" },
        { status: 400 }
      );
    }

    const updateData: any = {
      title,
      slug,
      content,
      excerpt,
      coverImage,
      status: status || "draft",
    };

    // Set publishedAt if status is being changed to published
    if (status === "published" && existingArticle[0].status !== "published") {
      updateData.publishedAt = new Date();
    }

    await db
      .update(articles)
      .set(updateData)
      .where(eq(articles.id, params.id));

    return NextResponse.json({ message: "Article updated successfully" });
  } catch (error) {
    console.error("Error updating article:", error);
    return NextResponse.json(
      { error: "Failed to update article" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const existingArticle = await db
      .select()
      .from(articles)
      .where(eq(articles.id, params.id))
      .limit(1);

    if (!existingArticle.length) {
      return NextResponse.json(
        { error: "Article not found" },
        { status: 404 }
      );
    }

    await db.delete(articles).where(eq(articles.id, params.id));

    return NextResponse.json({ message: "Article deleted successfully" });
  } catch (error) {
    console.error("Error deleting article:", error);
    return NextResponse.json(
      { error: "Failed to delete article" },
      { status: 500 }
    );
  }
}
