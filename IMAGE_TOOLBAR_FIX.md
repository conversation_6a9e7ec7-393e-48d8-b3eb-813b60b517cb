# Fix: Image Toolbar Menyebabkan Auto-Save dan <PERSON> Tertutup

## Root Cause Analysis

Masalah utama yang menyebabkan halaman edit tertutup dan otomatis tersimpan ketika memilih format gambar adalah **event bubbling dan form submission yang tidak sengaja**:

### 1. **Event Propagation Issues**
- Button dalam `ImageToolbar` tidak memiliki `type="button"` eksplisit
- Event dari button bubble up ke parent `<form>` element
- Browser menganggap click sebagai form submission

### 2. **Form Wrapper Context**
- `ArticleForm` membungkus seluruh editor dalam `<form>` element
- TipTap editor berada di dalam form context
- Setiap button click tanpa `type="button"` memicu form submit

### 3. **onBlur Auto-Update**
- Input fields dalam popover menggunakan `onBlur` untuk update
- onBlur bisa memicu event yang dianggap sebagai form interaction
- Tanpa `preventDefault()`, bisa memicu form submission

### 4. **Missing Event Prevention**
- Tidak ada `preventDefault()` dan `stopPropagation()`
- Event dari image toolbar bisa bubble up ke form parent
- Router.push() dalam form submission handler menyebabkan redirect

## Solutions Implemented

### 1. **Fixed Button Types**
```tsx
// BEFORE
<Button onClick={() => updateAlignment('left')}>

// AFTER  
<Button 
  type="button" 
  onClick={(e) => updateAlignment('left', e)}
>
```

### 2. **Added Event Prevention**
```tsx
const updateAlignment = (align: 'left' | 'center' | 'right', event?: React.MouseEvent) => {
  // Prevent event propagation to avoid form submission
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }
  // ... rest of function
};
```

### 3. **Enhanced Container Event Handling**
```tsx
<div
  className="fixed z-50 bg-white border rounded-lg shadow-lg p-2"
  onClick={(e) => {
    // Prevent any clicks from bubbling up to form
    e.preventDefault();
    e.stopPropagation();
  }}
  onMouseDown={(e) => {
    // Prevent any mouse events from bubbling up
    e.stopPropagation();
  }}
>
```

### 4. **Form-Level Protection**
```tsx
<form 
  onSubmit={handleSubmit} 
  className="space-y-6"
  onKeyDown={(e) => {
    // Prevent form submission on Enter key in input fields
    if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
      e.preventDefault();
    }
  }}
>
```

### 5. **Type Safety Improvements**
```tsx
// BEFORE
interface ImageToolbarProps {
  imageNode: any;
}

// AFTER
import { Node } from '@tiptap/pm/model';

interface ImageToolbarProps {
  imageNode: Node | null;
}
```

## Files Modified

1. **`components/editor/image-toolbar.tsx`**
   - ✅ Added `type="button"` to all buttons
   - ✅ Added event prevention to all click handlers
   - ✅ Enhanced container event handling
   - ✅ Improved type safety with `Node | null`

2. **`components/editor/gutenberg-editor.tsx`**
   - ✅ Added click event handling to prevent toolbar event bubbling
   - ✅ Maintained existing NaN validation fixes

3. **`components/admin/article-form.tsx`**
   - ✅ Added form-level keydown prevention
   - ✅ Fixed Next.js Image usage
   - ✅ Removed unused imports

## Testing Checklist

- [ ] Click alignment buttons (Left, Center, Right) - should NOT close page
- [ ] Click resize buttons (Reset, Fit to Width) - should NOT close page  
- [ ] Edit width/height in popover settings - should NOT close page
- [ ] Edit alt text in popover - should NOT close page
- [ ] Delete image - should work normally
- [ ] Download image - should work normally
- [ ] Normal form submission (Save/Update) - should work normally
- [ ] Cancel button - should work normally

## Prevention Strategy

Untuk mencegah masalah serupa di masa depan:

1. **Selalu gunakan `type="button"`** untuk button yang bukan submit
2. **Implementasikan event prevention** dalam toolbar/popup components
3. **Hindari nesting interactive elements** dalam form tanpa proper event handling
4. **Test interaction** antara form context dan editor tools
5. **Validate event propagation** dalam component testing

## Performance Impact

- ✅ **Minimal overhead**: Event prevention memiliki cost yang sangat kecil
- ✅ **Better UX**: Mencegah navigasi yang tidak diinginkan  
- ✅ **Type safety**: Mengurangi runtime errors
- ✅ **Maintainability**: Code lebih eksplisit dan mudah debug
