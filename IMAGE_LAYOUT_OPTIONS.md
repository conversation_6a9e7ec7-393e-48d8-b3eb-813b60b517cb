# Image Layout Options Feature

## Overview
Menambahkan opsi tata letak (layout) yang komprehensif pada image toolbar untuk memberikan kontrol yang lebih baik terhadap tampilan gambar dalam editor.

## New Features Added

### 1. **Layout Presets Button**
- Icon: Layout (dari <PERSON>)
- Posisi: Antara resize controls dan settings
- Fungsi: Membuka popover dengan berbagai opsi layout

### 2. **Quick Size Layouts**
```tsx
// Size presets yang tersedia:
- Small: 300px width, auto height
- Medium: 500px width, auto height  
- Large: 800px width, auto height
```

### 3. **Aspect Ratio Layouts**
```tsx
// Aspect ratio presets:
- Square: 400x400px (1:1 ratio)
- Wide: 600x300px (2:1 ratio)
- Tall: 400x600px (2:3 ratio)
```

### 4. **Special Layouts**
```tsx
// Special layout presets:
- Responsive: 100% width, auto height
- Banner: 100% width, 250px height
```

## Implementation Details

### Layout Preset Function
```tsx
const setLayoutPreset = (preset: 'small' | 'medium' | 'large' | 'square' | 'wide' | 'tall' | 'responsive' | 'banner', event?: React.MouseEvent) => {
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }

  // Update image node with new dimensions
  const { state } = editor;
  const { doc } = state;
  const imageSrc = imageNode?.attrs?.src;

  if (!imageSrc) return;

  let found = false;
  const tr = state.tr;
  let newWidth: string;
  let newHeight: string;

  // Define layout presets based on selection
  switch (preset) {
    case 'responsive':
      newWidth = '100%';
      newHeight = 'auto';
      break;
    // ... other cases
  }

  // Apply changes to editor
  doc.descendants((node, pos) => {
    if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
      tr.setNodeMarkup(pos, undefined, {
        ...node.attrs,
        width: newWidth,
        height: newHeight,
      });
      found = true;
      return false;
    }
  });

  if (found) {
    editor.view.dispatch(tr);
    // Update local state for immediate UI feedback
    setWidth(newWidth);
    setHeight(newHeight);
  }
};
```

### UI Layout Structure
```tsx
<Popover>
  <PopoverTrigger asChild>
    <Button type="button" variant="ghost" size="sm" title="Layout Options">
      <Layout className="h-4 w-4" />
    </Button>
  </PopoverTrigger>
  <PopoverContent className="w-64">
    <div className="space-y-3">
      {/* Quick Layouts Section */}
      <div>
        <Label className="text-sm font-medium">Quick Layouts</Label>
      </div>
      <div className="grid grid-cols-3 gap-2">
        {/* Size preset buttons */}
      </div>

      {/* Aspect Ratios Section */}
      <div>
        <Label className="text-sm font-medium">Aspect Ratios</Label>
      </div>
      <div className="grid grid-cols-3 gap-2">
        {/* Aspect ratio buttons */}
      </div>

      {/* Special Layouts Section */}
      <div>
        <Label className="text-sm font-medium">Special Layouts</Label>
      </div>
      <div className="grid grid-cols-2 gap-2">
        {/* Special layout buttons */}
      </div>
    </div>
  </PopoverContent>
</Popover>
```

## CSS Enhancements

### Responsive Layout Support
```css
/* Responsive layout */
.ProseMirror .image-wrapper .custom-image[style*="width: 100%"] {
  max-width: 100%;
  height: auto;
}

/* Banner style layout */
.ProseMirror .image-wrapper .custom-image[style*="height: 250px"] {
  object-fit: cover;
  object-position: center;
}

/* Square layout */
.ProseMirror .image-wrapper .custom-image[style*="400px"][style*="400px"] {
  object-fit: cover;
  object-position: center;
}
```

### Hover Effects
```css
.ProseMirror .image-wrapper:hover .custom-image {
  transform: scale(1.02);
}

@media (max-width: 768px) {
  .ProseMirror .image-wrapper .custom-image {
    transform: none !important;
  }
}
```

## User Experience

### Layout Options UI
- **Organized Sections**: Quick Layouts, Aspect Ratios, Special Layouts
- **Visual Icons**: Menggunakan Lucide icons yang sesuai (Square, RectangleHorizontal, dll)
- **Tooltips**: Setiap button memiliki tooltip informatif
- **Grid Layout**: Buttons tersusun dalam grid yang rapi
- **Immediate Feedback**: Perubahan langsung terlihat di editor

### Event Handling
- **Prevent Form Submission**: Semua button memiliki `preventDefault()` dan `stopPropagation()`
- **Type Safety**: Menggunakan explicit button `type="button"`
- **State Synchronization**: Local state terupdate setelah perubahan layout

## Layout Presets Available

### Quick Layouts (Size-based)
1. **Small** - 300px × auto
   - Ideal untuk thumbnail atau small illustrations
   - Icon: Small square

2. **Medium** - 500px × auto  
   - Default size untuk most content images
   - Icon: Medium square

3. **Large** - 800px × auto
   - High-impact images atau hero images
   - Icon: Large square

### Aspect Ratios
1. **Square** - 400px × 400px (1:1)
   - Profile pictures, logos, social media images
   - Icon: Square

2. **Wide** - 600px × 300px (2:1)
   - Landscape photos, headers
   - Icon: RectangleHorizontal

3. **Tall** - 400px × 600px (2:3)
   - Portrait photos, mobile screenshots
   - Icon: RectangleVertical

### Special Layouts
1. **Responsive** - 100% × auto
   - Adapts to container width
   - Icon: Maximize2

2. **Banner** - 100% × 250px
   - Banner-style images dengan fixed height
   - Icon: RectangleHorizontal

## Benefits

### For Content Creators
- **Quick Setup**: One-click layout presets
- **Consistent Sizing**: Predefined dimensions ensure consistency
- **Responsive Design**: Built-in responsive options
- **Professional Layouts**: Aspect ratios untuk different use cases

### For Developers
- **Extensible**: Easy to add new presets
- **Type Safe**: Full TypeScript support
- **Event Safe**: Proper event handling prevents form issues
- **CSS Integration**: Works dengan existing styling system

### For End Users
- **Better Reading Experience**: Properly sized images
- **Mobile Friendly**: Responsive layouts work on all devices
- **Faster Loading**: Appropriate sizing reduces bandwidth
- **Professional Appearance**: Consistent image layouts

## Files Modified

1. **`components/editor/image-toolbar.tsx`**
   - ✅ Added layout preset function
   - ✅ Added layout options UI
   - ✅ Enhanced event handling
   - ✅ Added new imports for icons

2. **`app/globals.css`**
   - ✅ Added CSS support for new layouts
   - ✅ Enhanced responsive behavior
   - ✅ Added hover effects

## Usage Instructions

1. **Select an Image** in the editor
2. **Click the Layout button** (📐 icon) in the image toolbar
3. **Choose a preset**:
   - Quick Layouts: Small, Medium, Large
   - Aspect Ratios: Square, Wide, Tall  
   - Special: Responsive, Banner
4. **Image updates immediately** with the new dimensions

## Future Enhancements

Potential additions for future versions:
- Custom aspect ratio input
- Crop tool integration
- Image filters and effects
- Layout templates with multiple images
- Advanced responsive breakpoints
