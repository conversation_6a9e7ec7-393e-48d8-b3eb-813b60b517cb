"use client";

import { useState, useEffect, useCallback } from 'react';

interface SimpleImageResizerProps {
  imageElement: HTMLImageElement;
  onResize: (width: number, height: number) => void;
}

export function SimpleImageResizer({ imageElement, onResize }: SimpleImageResizerProps) {
  const [isResizing, setIsResizing] = useState(false);
  const [currentSize, setCurrentSize] = useState({ width: 0, height: 0 });
  const [position, setPosition] = useState({ top: 0, left: 0 });

  useEffect(() => {
    if (imageElement) {
      let timeoutId: NodeJS.Timeout;

      const updatePositionAndSize = () => {
        const rect = imageElement.getBoundingClientRect();
        
        // Ensure we have valid dimensions
        const validWidth = rect.width > 0 ? rect.width : 200;
        const validHeight = rect.height > 0 ? rect.height : 150;
        
        setCurrentSize({ 
          width: validWidth, 
          height: validHeight 
        });

        // Calculate absolute position using scroll offsets
        setPosition({
          top: rect.top + window.scrollY,
          left: rect.left + window.scrollX
        });
      };

      const debouncedUpdate = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(updatePositionAndSize, 16); // ~60fps
      };

      // Update position immediately
      updatePositionAndSize();

      // Also update on scroll and resize with debouncing
      window.addEventListener('scroll', debouncedUpdate, true);
      window.addEventListener('resize', debouncedUpdate);
      
      return () => {
        clearTimeout(timeoutId);
        window.removeEventListener('scroll', debouncedUpdate, true);
        window.removeEventListener('resize', debouncedUpdate);
      };
    }
  }, [imageElement]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!imageElement) return;

    const rect = imageElement.getBoundingClientRect();
    const startPos = { x: e.clientX, y: e.clientY };
    const startSize = { width: rect.width, height: rect.height };
    
    // Ensure we have valid dimensions to avoid NaN
    if (startSize.width <= 0 || startSize.height <= 0) {
      console.warn('Invalid image dimensions for resizing');
      return;
    }
    
    const aspectRatio = startSize.width / startSize.height;
    
    // Additional validation for aspect ratio
    if (!isFinite(aspectRatio) || aspectRatio <= 0) {
      console.warn('Invalid aspect ratio calculated');
      return;
    }
    
    const minSize = 50;
    const maxSize = 800;

    setIsResizing(true);
    document.body.style.userSelect = 'none';
    document.body.style.cursor = 'se-resize';

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startPos.x;
      const deltaY = e.clientY - startPos.y;

      // Use the larger delta for more responsive resizing
      const delta = Math.abs(deltaX) > Math.abs(deltaY) ? deltaX : deltaY;

      const newWidth = Math.max(minSize, Math.min(maxSize, startSize.width + delta));
      const newHeight = newWidth / aspectRatio;

      // Validate calculated values to prevent NaN
      if (!isFinite(newWidth) || !isFinite(newHeight) || newWidth <= 0 || newHeight <= 0) {
        console.warn('Invalid dimensions calculated during resize');
        return;
      }

      // Update the current size state for display
      setCurrentSize({ width: newWidth, height: newHeight });

      // Apply resize immediately to the image for visual feedback
      imageElement.style.width = `${newWidth}px`;
      imageElement.style.height = `${newHeight}px`;

      // Call the callback with rounded values
      onResize(Math.round(newWidth), Math.round(newHeight));
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.userSelect = '';
      document.body.style.cursor = '';
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [imageElement, onResize]);

  if (!imageElement) return null;

  // Use the stored position and size
  return (
    <div
      className="fixed pointer-events-none z-50 select-none"
      style={{
        top: position.top,
        left: position.left,
        width: currentSize.width,
        height: currentSize.height,
      }}
    >
      {/* Simple resize handle - bottom right corner */}
      <div
        className="absolute w-4 h-4 bg-blue-500 border-2 border-white rounded cursor-se-resize pointer-events-auto hover:bg-blue-600 transition-colors shadow-lg"
        style={{ bottom: -8, right: -8 }}
        onMouseDown={handleMouseDown}
        title="Drag to resize"
      />
      
      {/* Selection border */}
      <div className="absolute inset-0 border-2 border-blue-500 pointer-events-none rounded opacity-80" />
      
      {/* Size indicator */}
      {isResizing && (
        <div className="absolute -top-8 left-0 bg-black text-white text-xs px-2 py-1 rounded pointer-events-none shadow-lg whitespace-nowrap">
          {Math.round(currentSize.width)} × {Math.round(currentSize.height)}
        </div>
      )}
    </div>
  );
}
