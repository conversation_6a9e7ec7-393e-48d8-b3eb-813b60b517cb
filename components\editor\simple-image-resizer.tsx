"use client";

import { useState, useEffect } from 'react';

interface SimpleImageResizerProps {
  imageElement: HTMLImageElement;
  onResize: (width: number, height: number) => void;
}

export function SimpleImageResizer({ imageElement, onResize }: SimpleImageResizerProps) {
  const [isResizing, setIsResizing] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [startSize, setStartSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (imageElement) {
      const rect = imageElement.getBoundingClientRect();
      setStartSize({ width: rect.width, height: rect.height });
    }
  }, [imageElement]);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('Resize handle clicked');

    const rect = imageElement.getBoundingClientRect();
    const startPos = { x: e.clientX, y: e.clientY };
    const startSize = { width: rect.width, height: rect.height };
    const aspectRatio = startSize.width / startSize.height;

    setIsResizing(true);

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startPos.x;
      const deltaY = e.clientY - startPos.y;

      // Use the larger delta for more responsive resizing
      const delta = Math.abs(deltaX) > Math.abs(deltaY) ? deltaX : deltaY;

      let newWidth = Math.max(50, Math.min(800, startSize.width + delta));
      let newHeight = newWidth / aspectRatio;

      console.log('Resizing:', { newWidth, newHeight, delta });

      // Apply resize immediately to the image
      imageElement.style.width = `${newWidth}px`;
      imageElement.style.height = `${newHeight}px`;

      // Call the callback
      onResize(newWidth, newHeight);
    };

    const handleMouseUp = () => {
      console.log('Resize ended');
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  if (!imageElement) return null;

  const rect = imageElement.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  return (
    <div
      className="fixed pointer-events-none z-50"
      style={{
        top: rect.top + scrollTop,
        left: rect.left + scrollLeft,
        width: rect.width,
        height: rect.height,
      }}
    >
      {/* Simple resize handle - bottom right corner */}
      <div
        className="absolute w-4 h-4 bg-blue-500 border-2 border-white rounded cursor-se-resize pointer-events-auto hover:bg-blue-600 transition-colors shadow-lg"
        style={{ bottom: -8, right: -8 }}
        onMouseDown={handleMouseDown}
        title="Drag to resize"
      />
      
      {/* Selection border */}
      <div className="absolute inset-0 border-2 border-blue-500 pointer-events-none rounded" />
      
      {/* Size indicator */}
      {isResizing && (
        <div className="absolute -top-8 left-0 bg-black text-white text-xs px-2 py-1 rounded pointer-events-none">
          {Math.round(rect.width)} × {Math.round(rect.height)}
        </div>
      )}
    </div>
  );
}
