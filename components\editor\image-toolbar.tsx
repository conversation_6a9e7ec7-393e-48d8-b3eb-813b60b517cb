"use client";

import { Editor } from '@tiptap/react';
import { Node } from '@tiptap/pm/model';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  AlignLeft,
  AlignCenter,
  AlignRight,
  Settings,
  Trash2,
  Download,
  RotateCcw,
  Maximize2,
  Layout,
  Square,
  RectangleHorizontal,
  RectangleVertical,
} from 'lucide-react';
import { useState, useEffect } from 'react';

interface ImageToolbarProps {
  editor: Editor;
  imageNode: Node | null;
  position: { top: number; left: number };
  onClose: () => void;
}

export function ImageToolbar({ editor, imageNode, position, onClose }: ImageToolbarProps) {
  const [width, setWidth] = useState(imageNode?.attrs?.width || '');
  const [height, setHeight] = useState(imageNode?.attrs?.height || '');
  const [alt, setAlt] = useState(imageNode?.attrs?.alt || '');
  const currentAlign = imageNode?.attrs?.align || 'center';

  useEffect(() => {
    setWidth(imageNode?.attrs?.width || '');
    setHeight(imageNode?.attrs?.height || '');
    setAlt(imageNode?.attrs?.alt || '');
  }, [imageNode]);

  const updateAlignment = (align: 'left' | 'center' | 'right', event?: React.MouseEvent) => {
    // Prevent event propagation to avoid form submission
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Find and update the image with matching src
    const { state } = editor;
    const { doc } = state;
    const imageSrc = imageNode?.attrs?.src;

    if (!imageSrc) return;

    let found = false;
    const tr = state.tr;

    doc.descendants((node, pos) => {
      if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
        tr.setNodeMarkup(pos, undefined, {
          ...node.attrs,
          align,
        });
        found = true;
        return false;
      }
    });

    if (found) {
      editor.view.dispatch(tr);
    }
  };

  const updateSize = (event?: React.FocusEvent) => {
    // Prevent form submission
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Find and update the image with matching src
    const { state } = editor;
    const { doc } = state;
    const imageSrc = imageNode?.attrs?.src;

    if (!imageSrc) return;

    let found = false;
    const tr = state.tr;

    doc.descendants((node, pos) => {
      if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
        tr.setNodeMarkup(pos, undefined, {
          ...node.attrs,
          width,
          height,
        });
        found = true;
        return false;
      }
    });

    if (found) {
      editor.view.dispatch(tr);
    }
  };

  const updateAlt = (event?: React.FocusEvent) => {
    // Prevent form submission
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Find and update the image with matching src
    const { state } = editor;
    const { doc } = state;
    const imageSrc = imageNode?.attrs?.src;

    if (!imageSrc) return;

    let found = false;
    const tr = state.tr;

    doc.descendants((node, pos) => {
      if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
        tr.setNodeMarkup(pos, undefined, {
          ...node.attrs,
          alt,
        });
        found = true;
        return false;
      }
    });

    if (found) {
      editor.view.dispatch(tr);
    }
  };

  const deleteImage = (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    editor.chain().focus().deleteSelection().run();
    onClose();
  };

  const downloadImage = () => {
    if (imageNode?.attrs?.src) {
      const link = document.createElement('a');
      link.href = imageNode.attrs.src;
      link.download = imageNode.attrs.alt || 'image';
      link.click();
    }
  };

  const resetSize = (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Reset to original size
    const { state } = editor;
    const { doc } = state;
    const imageSrc = imageNode?.attrs?.src;

    if (!imageSrc) return;

    let found = false;
    const tr = state.tr;

    doc.descendants((node, pos) => {
      if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
        tr.setNodeMarkup(pos, undefined, {
          ...node.attrs,
          width: undefined,
          height: undefined,
        });
        found = true;
        return false;
      }
    });

    if (found) {
      editor.view.dispatch(tr);
    }
  };

  const fitToWidth = (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Set width to 100% of container
    const { state } = editor;
    const { doc } = state;
    const imageSrc = imageNode?.attrs?.src;

    if (!imageSrc) return;

    let found = false;
    const tr = state.tr;

    doc.descendants((node, pos) => {
      if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
        tr.setNodeMarkup(pos, undefined, {
          ...node.attrs,
          width: '100%',
          height: 'auto',
        });
        found = true;
        return false;
      }
    });

    if (found) {
      editor.view.dispatch(tr);
    }
  };

  // Layout preset functions
  const setLayoutPreset = (preset: 'small' | 'medium' | 'large' | 'square' | 'wide' | 'tall' | 'responsive' | 'banner', event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    const { state } = editor;
    const { doc } = state;
    const imageSrc = imageNode?.attrs?.src;

    if (!imageSrc) return;

    let found = false;
    const tr = state.tr;
    let newWidth: string;
    let newHeight: string;

    // Define layout presets
    switch (preset) {
      case 'small':
        newWidth = '300px';
        newHeight = 'auto';
        break;
      case 'medium':
        newWidth = '500px';
        newHeight = 'auto';
        break;
      case 'large':
        newWidth = '800px';
        newHeight = 'auto';
        break;
      case 'square':
        newWidth = '400px';
        newHeight = '400px';
        break;
      case 'wide':
        newWidth = '600px';
        newHeight = '300px';
        break;
      case 'tall':
        newWidth = '400px';
        newHeight = '600px';
        break;
      case 'responsive':
        newWidth = '100%';
        newHeight = 'auto';
        break;
      case 'banner':
        newWidth = '100%';
        newHeight = '250px';
        break;
      default:
        newWidth = '500px';
        newHeight = 'auto';
    }

    doc.descendants((node, pos) => {
      if (node.type.name === 'customImage' && node.attrs.src === imageSrc && !found) {
        tr.setNodeMarkup(pos, undefined, {
          ...node.attrs,
          width: newWidth,
          height: newHeight,
        });
        found = true;
        return false;
      }
    });

    if (found) {
      editor.view.dispatch(tr);
      // Update local state
      setWidth(newWidth);
      setHeight(newHeight);
    }
  };

  return (
    <div
      className="fixed z-50 bg-white border rounded-lg shadow-lg p-2"
      style={{
        top: position.top - 60,
        left: position.left,
        transform: 'translateX(-50%)',
      }}
      onClick={(e) => {
        // Prevent any clicks from bubbling up to form
        e.preventDefault();
        e.stopPropagation();
      }}
      onMouseDown={(e) => {
        // Prevent any mouse events from bubbling up
        e.stopPropagation();
      }}
    >
      <div className="flex items-center gap-1">
        {/* Alignment Controls */}
        <div className="flex items-center gap-1">
          <Button
            type="button"
            variant={currentAlign === 'left' ? 'default' : 'ghost'}
            size="sm"
            onClick={(e) => updateAlignment('left', e)}
            title="Align Left"
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            variant={currentAlign === 'center' ? 'default' : 'ghost'}
            size="sm"
            onClick={(e) => updateAlignment('center', e)}
            title="Align Center"
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            variant={currentAlign === 'right' ? 'default' : 'ghost'}
            size="sm"
            onClick={(e) => updateAlignment('right', e)}
            title="Align Right"
          >
            <AlignRight className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Resize Controls */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={resetSize}
          title="Reset to Original Size"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={fitToWidth}
          title="Fit to Width"
        >
          <Maximize2 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Layout Presets */}
        <Popover>
          <PopoverTrigger asChild>
            <Button type="button" variant="ghost" size="sm" title="Layout Options">
              <Layout className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64">
            <div className="space-y-3">
              <div>
                <Label className="text-sm font-medium">Quick Layouts</Label>
              </div>
              
              {/* Size Presets */}
              <div className="grid grid-cols-3 gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => setLayoutPreset('small', e)}
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  title="Small (300px)"
                >
                  <Square className="h-3 w-3" />
                  <span className="text-xs">Small</span>
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => setLayoutPreset('medium', e)}
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  title="Medium (500px)"
                >
                  <Square className="h-4 w-4" />
                  <span className="text-xs">Medium</span>
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => setLayoutPreset('large', e)}
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  title="Large (800px)"
                >
                  <Square className="h-5 w-5" />
                  <span className="text-xs">Large</span>
                </Button>
              </div>

              {/* Aspect Ratio Presets */}
              <div>
                <Label className="text-sm font-medium">Aspect Ratios</Label>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => setLayoutPreset('square', e)}
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  title="Square (1:1)"
                >
                  <Square className="h-4 w-4" />
                  <span className="text-xs">Square</span>
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => setLayoutPreset('wide', e)}
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  title="Wide (2:1)"
                >
                  <RectangleHorizontal className="h-4 w-4" />
                  <span className="text-xs">Wide</span>
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => setLayoutPreset('tall', e)}
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  title="Tall (2:3)"
                >
                  <RectangleVertical className="h-4 w-4" />
                  <span className="text-xs">Tall</span>
                </Button>
              </div>

              {/* Special Layouts */}
              <div>
                <Label className="text-sm font-medium">Special Layouts</Label>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => setLayoutPreset('responsive', e)}
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  title="Responsive (100% width)"
                >
                  <Maximize2 className="h-4 w-4" />
                  <span className="text-xs">Responsive</span>
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => setLayoutPreset('banner', e)}
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  title="Banner Style"
                >
                  <RectangleHorizontal className="h-4 w-4" />
                  <span className="text-xs">Banner</span>
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <Separator orientation="vertical" className="h-6" />

        {/* Settings Popover */}
        <Popover>
          <PopoverTrigger asChild>
            <Button type="button" variant="ghost" size="sm" title="Image Settings">
              <Settings className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80">
            <div className="space-y-4">
              <div>
                <Label htmlFor="alt-text">Alt Text</Label>
                <Input
                  id="alt-text"
                  value={alt}
                  onChange={(e) => setAlt(e.target.value)}
                  onBlur={updateAlt}
                  placeholder="Describe this image"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="width">Width</Label>
                  <Input
                    id="width"
                    value={width}
                    onChange={(e) => setWidth(e.target.value)}
                    onBlur={updateSize}
                    placeholder="Auto"
                  />
                </div>
                <div>
                  <Label htmlFor="height">Height</Label>
                  <Input
                    id="height"
                    value={height}
                    onChange={(e) => setHeight(e.target.value)}
                    onBlur={updateSize}
                    placeholder="Auto"
                  />
                </div>
              </div>

              <div className="text-sm text-gray-500">
                <p><strong>Source:</strong> {imageNode?.attrs?.src}</p>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Download */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={downloadImage}
          title="Download Image"
        >
          <Download className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Delete */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={deleteImage}
          title="Delete Image"
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
