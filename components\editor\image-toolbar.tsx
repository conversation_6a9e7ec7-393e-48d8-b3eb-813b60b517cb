"use client";

import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  AlignLeft,
  AlignCenter,
  AlignRight,
  Settings,
  Trash2,
  Download,
} from 'lucide-react';
import { useState, useEffect } from 'react';

interface ImageToolbarProps {
  editor: Editor;
  imageNode: any;
  position: { top: number; left: number };
  onClose: () => void;
}

export function ImageToolbar({ editor, imageNode, position, onClose }: ImageToolbarProps) {
  const [width, setWidth] = useState(imageNode?.attrs?.width || '');
  const [height, setHeight] = useState(imageNode?.attrs?.height || '');
  const [alt, setAlt] = useState(imageNode?.attrs?.alt || '');
  const currentAlign = imageNode?.attrs?.align || 'center';

  useEffect(() => {
    setWidth(imageNode?.attrs?.width || '');
    setHeight(imageNode?.attrs?.height || '');
    setAlt(imageNode?.attrs?.alt || '');
  }, [imageNode]);

  const updateAlignment = (align: 'left' | 'center' | 'right') => {
    editor.chain().focus().updateImageAlign(align).run();
  };

  const updateSize = () => {
    editor.chain().focus().updateImageSize(width, height).run();
  };

  const updateAlt = () => {
    const { selection } = editor.state;
    const { from } = selection;
    const node = editor.state.doc.nodeAt(from);
    
    if (node && node.type.name === 'customImage') {
      editor.chain().focus().setNodeMarkup(from, undefined, {
        ...node.attrs,
        alt,
      }).run();
    }
  };

  const deleteImage = () => {
    editor.chain().focus().deleteSelection().run();
    onClose();
  };

  const downloadImage = () => {
    if (imageNode?.attrs?.src) {
      const link = document.createElement('a');
      link.href = imageNode.attrs.src;
      link.download = imageNode.attrs.alt || 'image';
      link.click();
    }
  };

  return (
    <div
      className="fixed z-50 bg-white border rounded-lg shadow-lg p-2"
      style={{
        top: position.top - 60,
        left: position.left,
        transform: 'translateX(-50%)',
      }}
    >
      <div className="flex items-center gap-1">
        {/* Alignment Controls */}
        <div className="flex items-center gap-1">
          <Button
            variant={currentAlign === 'left' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => updateAlignment('left')}
            title="Align Left"
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            variant={currentAlign === 'center' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => updateAlignment('center')}
            title="Align Center"
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            variant={currentAlign === 'right' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => updateAlignment('right')}
            title="Align Right"
          >
            <AlignRight className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Settings Popover */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="sm" title="Image Settings">
              <Settings className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80">
            <div className="space-y-4">
              <div>
                <Label htmlFor="alt-text">Alt Text</Label>
                <Input
                  id="alt-text"
                  value={alt}
                  onChange={(e) => setAlt(e.target.value)}
                  onBlur={updateAlt}
                  placeholder="Describe this image"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="width">Width</Label>
                  <Input
                    id="width"
                    value={width}
                    onChange={(e) => setWidth(e.target.value)}
                    onBlur={updateSize}
                    placeholder="Auto"
                  />
                </div>
                <div>
                  <Label htmlFor="height">Height</Label>
                  <Input
                    id="height"
                    value={height}
                    onChange={(e) => setHeight(e.target.value)}
                    onBlur={updateSize}
                    placeholder="Auto"
                  />
                </div>
              </div>

              <div className="text-sm text-gray-500">
                <p><strong>Source:</strong> {imageNode?.attrs?.src}</p>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Download */}
        <Button
          variant="ghost"
          size="sm"
          onClick={downloadImage}
          title="Download Image"
        >
          <Download className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Delete */}
        <Button
          variant="ghost"
          size="sm"
          onClick={deleteImage}
          title="Delete Image"
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
