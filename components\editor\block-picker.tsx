"use client";

import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Type,
  Image,
  List,
  Quote,
  Code,
  Table,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  Video,
  FileText,
} from 'lucide-react';

interface BlockPickerProps {
  editor: Editor;
}

export function BlockPicker({ editor }: BlockPickerProps) {
  const insertBlock = (type: string) => {
    switch (type) {
      case 'heading1':
        editor.chain().focus().toggleHeading({ level: 1 }).run();
        break;
      case 'heading2':
        editor.chain().focus().toggleHeading({ level: 2 }).run();
        break;
      case 'heading3':
        editor.chain().focus().toggleHeading({ level: 3 }).run();
        break;
      case 'paragraph':
        editor.chain().focus().setParagraph().run();
        break;
      case 'bulletList':
        editor.chain().focus().toggleBulletList().run();
        break;
      case 'orderedList':
        editor.chain().focus().toggleOrderedList().run();
        break;
      case 'blockquote':
        editor.chain().focus().toggleBlockquote().run();
        break;
      case 'codeBlock':
        editor.chain().focus().toggleCodeBlock().run();
        break;
      case 'table':
        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        break;
      case 'horizontalRule':
        editor.chain().focus().setHorizontalRule().run();
        break;
      case 'image':
        const url = window.prompt('Enter image URL:');
        if (url) {
          editor.chain().focus().setImage({ src: url }).run();
        }
        break;
    }
  };

  const blocks = [
    {
      category: 'Text',
      items: [
        { id: 'paragraph', label: 'Paragraph', icon: Type, description: 'Start with the building block of all narrative.' },
        { id: 'heading1', label: 'Heading 1', icon: Heading1, description: 'Introduce new sections and organize content.' },
        { id: 'heading2', label: 'Heading 2', icon: Heading2, description: 'Create a sub-section.' },
        { id: 'heading3', label: 'Heading 3', icon: Heading3, description: 'Create a sub-sub-section.' },
        { id: 'quote', label: 'Quote', icon: Quote, description: 'Give quoted text visual emphasis.' },
      ]
    },
    {
      category: 'Lists',
      items: [
        { id: 'bulletList', label: 'List', icon: List, description: 'Create a simple bulleted list.' },
        { id: 'orderedList', label: 'Numbered List', icon: List, description: 'Create a numbered list.' },
      ]
    },
    {
      category: 'Media',
      items: [
        { id: 'image', label: 'Image', icon: Image, description: 'Insert an image.' },
      ]
    },
    {
      category: 'Formatting',
      items: [
        { id: 'codeBlock', label: 'Code', icon: Code, description: 'Display code snippets.' },
        { id: 'table', label: 'Table', icon: Table, description: 'Insert a table.' },
        { id: 'horizontalRule', label: 'Separator', icon: AlignLeft, description: 'Create a break between ideas or sections.' },
      ]
    }
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Plus className="h-4 w-4" />
          Add Block
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80 max-h-96 overflow-y-auto">
        {blocks.map((category, categoryIndex) => (
          <div key={category.category}>
            {categoryIndex > 0 && <DropdownMenuSeparator />}
            <div className="px-2 py-1.5 text-sm font-semibold text-gray-500">
              {category.category}
            </div>
            {category.items.map((block) => (
              <DropdownMenuItem
                key={block.id}
                onClick={() => insertBlock(block.id)}
                className="flex items-start gap-3 p-3 cursor-pointer"
              >
                <block.icon className="h-5 w-5 mt-0.5 text-gray-600" />
                <div className="flex-1">
                  <div className="font-medium">{block.label}</div>
                  <div className="text-sm text-gray-500">{block.description}</div>
                </div>
              </DropdownMenuItem>
            ))}
          </div>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
