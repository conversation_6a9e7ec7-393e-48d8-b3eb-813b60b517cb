# Fix: Image Resizer Positioning Issue

## Problem
Garis biru image resizer tidak berada di posisi yang seharusnya - berada di luar area gambar yang sebenarnya.

## Root Cause Analysis

### 1. **Positioning Context Issues**
- Image resizer menggunakan positioning yang tidak konsisten
- Mixed usage antara `absolute` dan `fixed` positioning
- Tidak ada synchronization yang tepat dengan scroll position

### 2. **Container Reference Problems**
- Mencari container parent yang salah (`.border.rounded-lg`)
- Calculating relative position terhadap container yang tidak tepat
- Missing scroll offset calculations

### 3. **Update Timing Issues**
- Position tidak terupdate saat scroll atau resize
- No debouncing untuk smooth performance
- React state update yang tidak synchronized

## Solutions Implemented

### 1. **Fixed Positioning Strategy**
```tsx
// BEFORE: Mixed absolute/relative positioning
const editorContainer = imageElement.closest('.border.rounded-lg');
const relativeTop = rect.top - containerRect.top;

// AFTER: Consistent fixed positioning with scroll offsets
setPosition({
  top: rect.top + window.scrollY,
  left: rect.left + window.scrollX
});
```

### 2. **Enhanced Position Tracking**
```tsx
const [position, setPosition] = useState({ top: 0, left: 0 });

const updatePositionAndSize = () => {
  const rect = imageElement.getBoundingClientRect();
  
  // Calculate absolute position using scroll offsets
  setPosition({
    top: rect.top + window.scrollY,
    left: rect.left + window.scrollX
  });
};
```

### 3. **Debounced Updates for Performance**
```tsx
const debouncedUpdate = () => {
  clearTimeout(timeoutId);
  timeoutId = setTimeout(updatePositionAndSize, 16); // ~60fps
};

window.addEventListener('scroll', debouncedUpdate, true);
window.addEventListener('resize', debouncedUpdate);
```

### 4. **Improved Event Handling**
```tsx
// Listen to all scroll events with capture
window.addEventListener('scroll', debouncedUpdate, true);

// Cleanup with proper timeout clearing
return () => {
  clearTimeout(timeoutId);
  window.removeEventListener('scroll', debouncedUpdate, true);
  window.removeEventListener('resize', debouncedUpdate);
};
```

### 5. **Consistent Rendering**
```tsx
return (
  <div
    className="fixed pointer-events-none z-50 select-none"
    style={{
      top: position.top,
      left: position.left,
      width: currentSize.width,
      height: currentSize.height,
    }}
  >
```

## Technical Details

### Positioning Method
- **Fixed positioning** dengan calculated scroll offsets
- Real-time tracking dari `getBoundingClientRect()`
- Debounced updates untuk performance optimization

### Event Listeners
- `scroll` events dengan capture mode (true)
- `resize` events untuk window changes
- Debouncing dengan 16ms interval (~60fps)

### State Management
- `position` state untuk absolute coordinates
- `currentSize` state untuk current dimensions
- Synchronized updates untuk smooth tracking

## Files Modified

1. **`components/editor/simple-image-resizer.tsx`**
   - ✅ Added position state tracking
   - ✅ Implemented debounced position updates
   - ✅ Fixed positioning calculations with scroll offsets
   - ✅ Enhanced event listener management

2. **`components/editor/gutenberg-editor.tsx`**
   - ✅ Moved resizer back to outer container for proper fixed positioning
   - ✅ Maintained existing event prevention fixes

## Expected Behavior

- ✅ **Accurate positioning**: Garis biru akan tepat mengelilingi gambar
- ✅ **Scroll tracking**: Position mengikuti saat scroll page
- ✅ **Resize handle**: Corner handle tepat di sudut kanan bawah gambar
- ✅ **Smooth updates**: No laggy position updates
- ✅ **Performance**: Debounced updates prevent excessive re-renders

## Testing Checklist

- [ ] Click pada gambar - resizer muncul tepat di posisi gambar
- [ ] Scroll halaman - resizer ikut bergerak dengan gambar
- [ ] Resize window - resizer tetap dalam posisi yang benar
- [ ] Drag corner handle - gambar resize dengan smooth
- [ ] Multiple images - switching antar gambar bekerja dengan benar

## Performance Optimizations

1. **Debouncing**: 16ms interval untuk ~60fps smooth updates
2. **Event Capture**: Menangkap semua scroll events termasuk nested
3. **Timeout Cleanup**: Proper cleanup untuk prevent memory leaks
4. **Efficient Calculations**: Minimal DOM queries per update
