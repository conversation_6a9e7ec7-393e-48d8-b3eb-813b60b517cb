'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Node } from '@tiptap/pm/model';

import { 
  Bold, 
  Italic, 
  Underline, 
  AlignLeft, 
  AlignCenter, 
  AlignRight, 
  AlignJustify,
  Image,
  Upload,
  RotateCcw,
  RotateCw,
  Move,
  Maximize2,
  Type,
  Link as LinkIcon,
  List,
  ListOrdered,
  Quote,
  Code,
  Undo,
  Redo,
  X,
  WrapText,
  Square,
  Space,
  ChevronDown,
  Trash2,
  Settings,
  Download,
  Layout,
  RectangleHorizontal,
  RectangleVertical
} from 'lucide-react';

import { cn } from '@/lib/utils';

const ResizeHandles = ({ selectedImageElement, editorRef, onMouseDown }) => {
  const [position, setPosition] = useState({ left: 0, top: 0, width: 0, height: 0 });

  React.useEffect(() => {
    if (selectedImageElement && editorRef.current) {
      const updatePosition = () => {
        const imgRect = selectedImageElement.getBoundingClientRect();
        const editorRect = editorRef.current.getBoundingClientRect();
        
        setPosition({
          left: imgRect.left - editorRect.left - 5,
          top: imgRect.top - editorRect.top - 5,
          width: selectedImageElement.offsetWidth + 10,
          height: selectedImageElement.offsetHeight + 10
        });
      };

      updatePosition();
      
      // Update position when scrolling or resizing
      const handleUpdate = () => {
        requestAnimationFrame(updatePosition);
      };
      
      window.addEventListener('scroll', handleUpdate);
      window.addEventListener('resize', handleUpdate);
      
      // Create a simple interval to update position during resize
      const interval = setInterval(updatePosition, 16); // ~60fps
      
      return () => {
        window.removeEventListener('scroll', handleUpdate);
        window.removeEventListener('resize', handleUpdate);
        clearInterval(interval);
      };
    }
  }, [selectedImageElement, editorRef]);

  if (!selectedImageElement) return null;

  return (
    <div 
      className="absolute pointer-events-none z-20"
      style={{
        left: position.left,
        top: position.top,
        width: position.width,
        height: position.height,
      }}
    >
      {/* Corner handles */}
      <div className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 border border-white cursor-nw-resize pointer-events-auto rounded-sm shadow-sm"
           onMouseDown={(e) => onMouseDown(e, 'nw')} />
      <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 border border-white cursor-ne-resize pointer-events-auto rounded-sm shadow-sm"
           onMouseDown={(e) => onMouseDown(e, 'ne')} />
      <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 border border-white cursor-sw-resize pointer-events-auto rounded-sm shadow-sm"
           onMouseDown={(e) => onMouseDown(e, 'sw')} />
      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 border border-white cursor-se-resize pointer-events-auto rounded-sm shadow-sm"
           onMouseDown={(e) => onMouseDown(e, 'se')} />
      
      {/* Edge handles */}
      <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 border border-white cursor-n-resize pointer-events-auto rounded-sm shadow-sm"
           onMouseDown={(e) => onMouseDown(e, 'n')} />
      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 border border-white cursor-s-resize pointer-events-auto rounded-sm shadow-sm"
           onMouseDown={(e) => onMouseDown(e, 's')} />
      <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 border border-white cursor-w-resize pointer-events-auto rounded-sm shadow-sm"
           onMouseDown={(e) => onMouseDown(e, 'w')} />
      <div className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 border border-white cursor-e-resize pointer-events-auto rounded-sm shadow-sm"
           onMouseDown={(e) => onMouseDown(e, 'e')} />
    </div>
  );
};

const SpacingDropdown = ({ isOpen, onToggle, onSelect }) => {
  const spacingOptions = [
    { label: 'Single', value: '1', lineHeight: '1.0' },
    { label: '1.15', value: '1.15', lineHeight: '1.15' },
    { label: '1.5', value: '1.5', lineHeight: '1.5' },
    { label: 'Double', value: '2', lineHeight: '2.0' },
    { label: '2.5', value: '2.5', lineHeight: '2.5' },
    { label: 'Triple', value: '3', lineHeight: '3.0' }
  ];

  return (
    <div className="relative">
      <button
        onClick={onToggle}
        className="flex items-center gap-1 p-2 rounded hover:bg-gray-100 transition-colors text-gray-600"
        title="Line Spacing"
      >
        <Space size={18} />
        <ChevronDown size={14} />
      </button>
      
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-30 min-w-32">
          {spacingOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => onSelect(option.value)}
              className="w-full text-left px-3 py-2 hover:bg-gray-100 text-sm"
              style={{ lineHeight: option.lineHeight }}
            >
              {option.label}
            </button>
          ))}
          <div className="border-t border-gray-200 my-1"></div>
          <button
            onClick={() => onSelect('add-space-before')}
            className="w-full text-left px-3 py-2 hover:bg-gray-100 text-sm"
          >
            Add space before paragraph
          </button>
          <button
            onClick={() => onSelect('add-space-after')}
            className="w-full text-left px-3 py-2 hover:bg-gray-100 text-sm"
          >
            Add space after paragraph
          </button>
          <button
            onClick={() => onSelect('remove-space')}
            className="w-full text-left px-3 py-2 hover:bg-gray-100 text-sm"
          >
            Remove extra spacing
          </button>
        </div>
      )}
    </div>
  );
};

const GutenbergEditor = ({ content: initialContent = '', onChange, placeholder = 'Start writing your article...' }) => {
  const [content, setContent] = useState(initialContent);
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedImageElement, setSelectedImageElement] = useState(null);
  const [imageToolbarPosition, setImageToolbarPosition] = useState({ x: 0, y: 0 });
  const [showImageToolbar, setShowImageToolbar] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null);
  const [initialMousePos, setInitialMousePos] = useState({ x: 0, y: 0 });
  const [initialImageSize, setInitialImageSize] = useState({ width: 0, height: 0 });
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [showSpacingDropdown, setShowSpacingDropdown] = useState(false);
  const editorRef = useRef(null);
  const fileInputRef = useRef(null);

  // Update content when initialContent changes
  useEffect(() => {
    if (initialContent !== content) {
      setContent(initialContent);
      if (editorRef.current) {
        editorRef.current.innerHTML = initialContent;
      }
    }
  }, [initialContent]);

  // Initialize editor
  useEffect(() => {
    if (editorRef.current && initialContent) {
      editorRef.current.innerHTML = initialContent;
      setContent(initialContent);
    }
  }, []);

  // Handle editor focus
  const handleEditorFocus = () => {
    // Ensure we're focused on the editor
    if (editorRef.current && document.activeElement !== editorRef.current) {
      editorRef.current.focus();
    }
  };

  // Call onChange when content changes
  const handleContentChange = (newContent) => {
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
  };

  // Text formatting functions
  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value);
    saveToHistory();
  };

  const saveToHistory = () => {
    const newHistory = history.slice(0, historyIndex + 1);
    const currentContent = editorRef.current.innerHTML;
    newHistory.push(currentContent);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
    handleContentChange(currentContent);
  };

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      editorRef.current.innerHTML = history[historyIndex - 1];
    }
  };

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      editorRef.current.innerHTML = history[historyIndex + 1];
    }
  };

  // Spacing functions
  const handleSpacingSelect = (option) => {
    const selection = window.getSelection();
    
    if (option.startsWith('add-space-')) {
      // Add spacing before or after paragraph
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const paragraph = range.commonAncestorContainer.nodeType === Node.TEXT_NODE 
          ? range.commonAncestorContainer.parentElement 
          : range.commonAncestorContainer;
        
        if (option === 'add-space-before') {
          const spacer = document.createElement('div');
          spacer.style.height = '1em';
          spacer.innerHTML = '&nbsp;';
          paragraph.parentNode.insertBefore(spacer, paragraph);
        } else if (option === 'add-space-after') {
          const spacer = document.createElement('div');
          spacer.style.height = '1em';
          spacer.innerHTML = '&nbsp;';
          paragraph.parentNode.insertBefore(spacer, paragraph.nextSibling);
        }
      }
    } else if (option === 'remove-space') {
      // Remove extra spacing elements
      const spacers = editorRef.current.querySelectorAll('div[style*="height: 1em"]');
      spacers.forEach(spacer => spacer.remove());
    } else {
      // Set line height
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        if (range.toString().length > 0) {
          // Apply to selected text
          const span = document.createElement('span');
          span.style.lineHeight = option;
          range.surroundContents(span);
        } else {
          // Apply to current paragraph
          const paragraph = range.commonAncestorContainer.nodeType === Node.TEXT_NODE 
            ? range.commonAncestorContainer.parentElement 
            : range.commonAncestorContainer;
          
          if (paragraph && paragraph !== editorRef.current) {
            paragraph.style.lineHeight = option;
          } else {
            // Apply to entire editor if no specific paragraph
            editorRef.current.style.lineHeight = option;
          }
        }
      }
    }
    
    setShowSpacingDropdown(false);
    saveToHistory();
  };

  // Image handling
  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const data = await response.json();
          const img = {
            src: data.url,
            name: file.name,
            size: file.size,
            id: Date.now()
          };
          insertImage(img);
        } else {
          console.error('Upload failed');
        }
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    }
  };

  const insertImage = (img) => {
    const imageElement = createImageElement(img);

    // Always insert at the end of the editor content
    if (editorRef.current) {
      // Focus the editor first
      editorRef.current.focus();

      // Insert at the end
      editorRef.current.appendChild(imageElement);

      // Add a line break after the image
      const br = document.createElement('br');
      editorRef.current.appendChild(br);

      // Set cursor after the image
      const range = document.createRange();
      const selection = window.getSelection();
      range.setStartAfter(br);
      range.setEndAfter(br);
      selection.removeAllRanges();
      selection.addRange(range);
    }

    saveToHistory();
  };

  const createImageElement = (img) => {
    const container = document.createElement('div');
    container.className = 'image-container';
    container.contentEditable = false;

    container.style.cssText = `
      display: block;
      margin: 15px 0;
      text-align: center;
      position: relative;
      box-sizing: border-box;
      clear: both;
    `;

    const imageEl = document.createElement('img');
    imageEl.src = img.src;
    imageEl.alt = img.name;
    imageEl.draggable = false;
    imageEl.style.cssText = `
      max-width: 100%;
      height: auto;
      border-radius: 4px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: border-color 0.2s;
      display: block;
      margin: 0 auto;
    `;

    imageEl.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      handleImageClick(imageEl, img);
    });

    container.appendChild(imageEl);
    return container;
  };

  const handleImageClick = (imageElement, imgData) => {
    // Remove previous selection
    document.querySelectorAll('.image-container img').forEach(i => {
      i.style.border = '2px solid transparent';
      i.parentElement.style.outline = 'none';
    });
    
    // Select current image
    imageElement.style.border = '2px solid #3b82f6';
    imageElement.parentElement.style.outline = '2px solid #3b82f6';
    
    setSelectedImage(imgData);
    setSelectedImageElement(imageElement);
    
    // Calculate toolbar position
    const updateToolbarPosition = () => {
      const rect = imageElement.getBoundingClientRect();
      const editorRect = editorRef.current.getBoundingClientRect();
      
      setImageToolbarPosition({
        x: rect.left - editorRect.left,
        y: rect.top - editorRect.top - 50
      });
    };
    
    updateToolbarPosition();
    setShowImageToolbar(true);
  };

  const hideImageToolbar = () => {
    setShowImageToolbar(false);
    setSelectedImage(null);
    setSelectedImageElement(null);
    
    // Remove selection styling
    document.querySelectorAll('.image-container img').forEach(i => {
      i.style.border = '2px solid transparent';
      i.parentElement.style.outline = 'none';
    });
  };

  // Image resize handlers
  const handleMouseDown = useCallback((e, handle) => {
    if (!selectedImageElement) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const currentWidth = selectedImageElement.offsetWidth;
    const currentHeight = selectedImageElement.offsetHeight;
    
    setIsResizing(true);
    setResizeHandle(handle);
    setInitialMousePos({ x: e.clientX, y: e.clientY });
    setInitialImageSize({ width: currentWidth, height: currentHeight });
    
    const handleMouseMove = (moveEvent) => {
      if (!selectedImageElement) return;
      
      const deltaX = moveEvent.clientX - e.clientX;
      const deltaY = moveEvent.clientY - e.clientY;
      
      let newWidth = currentWidth;
      let newHeight = currentHeight;
      
      switch (handle) {
        case 'se': // bottom-right
          newWidth = Math.max(50, currentWidth + deltaX);
          newHeight = Math.max(50, currentHeight + deltaY);
          break;
        case 'sw': // bottom-left
          newWidth = Math.max(50, currentWidth - deltaX);
          newHeight = Math.max(50, currentHeight + deltaY);
          break;
        case 'ne': // top-right
          newWidth = Math.max(50, currentWidth + deltaX);
          newHeight = Math.max(50, currentHeight - deltaY);
          break;
        case 'nw': // top-left
          newWidth = Math.max(50, currentWidth - deltaX);
          newHeight = Math.max(50, currentHeight - deltaY);
          break;
        case 'e': // right
          newWidth = Math.max(50, currentWidth + deltaX);
          break;
        case 'w': // left
          newWidth = Math.max(50, currentWidth - deltaX);
          break;
        case 's': // bottom
          newHeight = Math.max(50, currentHeight + deltaY);
          break;
        case 'n': // top
          newHeight = Math.max(50, currentHeight - deltaY);
          break;
      }
      
      selectedImageElement.style.width = `${newWidth}px`;
      selectedImageElement.style.height = `${newHeight}px`;
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      setResizeHandle(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      saveToHistory();
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [selectedImageElement, saveToHistory]);

  // Image alignment functions
  const setImageAlignment = (alignment) => {
    if (!selectedImageElement) return;
    
    const container = selectedImageElement.parentElement;
    
    let containerStyles = '';
    
    switch (alignment) {
      case 'left':
        containerStyles = `
          float: left;
          margin: 0 15px 15px 0;
          clear: left;
          max-width: 50%;
          position: relative;
          box-sizing: border-box;
        `;
        break;
      case 'right':
        containerStyles = `
          float: right;
          margin: 0 0 15px 15px;
          clear: right;
          max-width: 50%;
          position: relative;
          box-sizing: border-box;
        `;
        break;
      case 'center':
        containerStyles = `
          display: block;
          text-align: center;
          margin: 15px auto;
          clear: both;
          max-width: 50%;
          position: relative;
          box-sizing: border-box;
        `;
        break;
    }
    
    container.style.cssText = containerStyles + `outline: 2px solid #3b82f6;`;
    saveToHistory();
  };

  const toggleTextWrap = () => {
    if (!selectedImageElement) return;
    
    const container = selectedImageElement.parentElement;
    const isCurrentlyWrapped = container.style.float !== '';
    
    if (isCurrentlyWrapped) {
      // Remove wrap (center align)
      container.style.cssText = `
        display: block;
        text-align: center;
        margin: 15px auto;
        clear: both;
        max-width: 50%;
        position: relative;
        box-sizing: border-box;
        outline: 2px solid #3b82f6;
      `;
    } else {
      // Add wrap (left align)
      container.style.cssText = `
        float: left;
        margin: 0 15px 15px 0;
        clear: left;
        max-width: 50%;
        position: relative;
        box-sizing: border-box;
        outline: 2px solid #3b82f6;
      `;
    }
    
    saveToHistory();
  };

  const deleteSelectedImage = () => {
    if (selectedImageElement) {
      const container = selectedImageElement.parentElement;
      if (container && container.className === 'image-container') {
        container.remove();
        hideImageToolbar();
        saveToHistory();
      }
    }
  };

  // Click outside handler
  const handleEditorClick = (e) => {
    // Only handle clicks within the editor
    if (!editorRef.current || !editorRef.current.contains(e.target)) {
      return;
    }

    if (!e.target.closest('.image-container') && !e.target.closest('.image-toolbar')) {
      hideImageToolbar();
    }
    // Close spacing dropdown when clicking in editor
    if (!e.target.closest('.spacing-dropdown')) {
      setShowSpacingDropdown(false);
    }
  };

  // Handle image upload button click
  const handleImageButtonClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Focus the editor first to ensure proper context
    if (editorRef.current) {
      editorRef.current.focus();
    }

    // Trigger file input
    fileInputRef.current?.click();
  };

  const ToolbarButton = ({ onClick, icon: Icon, title, active = false }) => (
    <button
      onClick={onClick}
      title={title}
      className={`p-2 rounded hover:bg-gray-100 transition-colors ${
        active ? 'bg-blue-100 text-blue-600' : 'text-gray-600'
      }`}
    >
      <Icon size={18} />
    </button>
  );

  const ImageToolbarButton = ({ onClick, icon: Icon, title, active = false }) => (
    <button
      onClick={onClick}
      title={title}
      className={`p-1.5 rounded hover:bg-gray-100 transition-colors ${
        active ? 'bg-blue-100 text-blue-600' : 'text-gray-600'
      }`}
    >
      <Icon size={16} />
    </button>
  );

  return (
    <div className="w-full bg-white">
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        {/* Main Toolbar */}
        <div className="bg-gray-50 border-b border-gray-300 p-2">
          <div className="flex flex-wrap gap-1">
            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <ToolbarButton onClick={undo} icon={Undo} title="Undo" />
              <ToolbarButton onClick={redo} icon={Redo} title="Redo" />
            </div>

            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <ToolbarButton 
                onClick={() => execCommand('bold')} 
                icon={Bold} 
                title="Bold" 
              />
              <ToolbarButton 
                onClick={() => execCommand('italic')} 
                icon={Italic} 
                title="Italic" 
              />
              <ToolbarButton 
                onClick={() => execCommand('underline')} 
                icon={Underline} 
                title="Underline" 
              />
            </div>

            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <ToolbarButton 
                onClick={() => execCommand('justifyLeft')} 
                icon={AlignLeft} 
                title="Align Left" 
              />
              <ToolbarButton 
                onClick={() => execCommand('justifyCenter')} 
                icon={AlignCenter} 
                title="Align Center" 
              />
              <ToolbarButton 
                onClick={() => execCommand('justifyRight')} 
                icon={AlignRight} 
                title="Align Right" 
              />
              <ToolbarButton 
                onClick={() => execCommand('justifyFull')} 
                icon={AlignJustify} 
                title="Justify" 
              />
            </div>

            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <ToolbarButton 
                onClick={() => execCommand('insertUnorderedList')} 
                icon={List} 
                title="Bullet List" 
              />
              <ToolbarButton 
                onClick={() => execCommand('insertOrderedList')} 
                icon={ListOrdered} 
                title="Numbered List" 
              />
              <ToolbarButton 
                onClick={() => execCommand('formatBlock', 'blockquote')} 
                icon={Quote} 
                title="Quote" 
              />
              <ToolbarButton 
                onClick={() => execCommand('formatBlock', 'pre')} 
                icon={Code} 
                title="Code Block" 
              />
            </div>

            <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
              <div className="spacing-dropdown">
                <SpacingDropdown 
                  isOpen={showSpacingDropdown}
                  onToggle={() => setShowSpacingDropdown(!showSpacingDropdown)}
                  onSelect={handleSpacingSelect}
                />
              </div>
            </div>

            <div className="flex gap-1">
              <ToolbarButton
                onClick={handleImageButtonClick}
                icon={Image}
                title="Insert Image"
              />
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>
          </div>
        </div>

        {/* Editor */}
        <div className="relative">
          <div
            ref={editorRef}
            contentEditable
            className="min-h-64 text-gray-900 p-4 focus:outline-none prose max-w-none relative"
            style={{
              lineHeight: '1.6',
              fontSize: '16px',
              textAlign: 'left',
              overflow: 'hidden'
            }}
            onInput={(e) => handleContentChange(e.target.innerHTML)}
            onClick={handleEditorClick}
            onFocus={handleEditorFocus}
            suppressContentEditableWarning={true}
            data-placeholder={placeholder}
          >
            {!content && (
              <div className="text-gray-400 pointer-events-none absolute top-4 left-4">
                {placeholder}
              </div>
            )}
          </div>

          {/* Image Toolbar */}
          {showImageToolbar && selectedImageElement && (
            <>
              {/* Resize handles */}
              <ResizeHandles 
                selectedImageElement={selectedImageElement}
                editorRef={editorRef}
                onMouseDown={handleMouseDown}
              />

              {/* Image Toolbar */}
              <div 
                className="image-toolbar absolute bg-white border border-gray-300 rounded shadow-lg p-2 z-10"
                style={{
                  left: Math.max(0, imageToolbarPosition.x),
                  top: Math.max(0, imageToolbarPosition.y)
                }}
              >
                <div className="flex items-center gap-1">
                  {/* Alignment */}
                  <ImageToolbarButton 
                    onClick={() => setImageAlignment('left')} 
                    icon={AlignLeft} 
                    title="Align Left" 
                  />
                  <ImageToolbarButton 
                    onClick={() => setImageAlignment('center')} 
                    icon={AlignCenter} 
                    title="Align Center" 
                  />
                  <ImageToolbarButton 
                    onClick={() => setImageAlignment('right')} 
                    icon={AlignRight} 
                    title="Align Right" 
                  />
                  
                  <div className="w-px h-6 bg-gray-300 mx-1"></div>
                  
                  {/* Text Wrap */}
                  <ImageToolbarButton 
                    onClick={toggleTextWrap} 
                    icon={WrapText} 
                    title="Toggle Text Wrap" 
                  />
                  
                  <div className="w-px h-6 bg-gray-300 mx-1"></div>
                  
                  {/* Delete Image */}
                  <ImageToolbarButton 
                    onClick={deleteSelectedImage} 
                    icon={Trash2} 
                    title="Delete Image" 
                  />
                  
                  <div className="w-px h-6 bg-gray-300 mx-1"></div>
                  
                  {/* Close */}
                  <ImageToolbarButton 
                    onClick={hideImageToolbar} 
                    icon={X} 
                    title="Close" 
                  />
                </div>
              </div>
            </>
          )}
        </div>

        {/* Status Bar */}
        <div className="bg-gray-50 border-t border-gray-300 px-4 py-2 text-sm text-gray-600">
          <div className="flex justify-between">
            <span>Ready</span>
            <span>{content.length} characters</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GutenbergEditor;