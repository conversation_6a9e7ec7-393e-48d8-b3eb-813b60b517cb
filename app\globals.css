@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* TipTap Editor Styles */
.ProseMirror {
  outline: none;
  padding: 1.5rem;
  min-height: 300px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
  color: #374151;
  max-width: none;
}

.ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  line-height: 1.2;
  color: #111827;
}

.ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  line-height: 1.3;
  color: #111827;
}

.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.5rem 0;
  line-height: 1.4;
  color: #111827;
}

.ProseMirror p {
  margin: 1rem 0;
  line-height: 1.8;
  color: #374151;
  text-align: justify;
  font-size: 1rem;
}

.ProseMirror ul, .ProseMirror ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.ProseMirror li {
  margin: 0.25rem 0;
}

.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.ProseMirror pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.ProseMirror pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
}

.ProseMirror th, .ProseMirror td {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

.ProseMirror th {
  background-color: #f9fafb;
  font-weight: bold;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  display: block;
}

/* Custom image wrapper styling */
.ProseMirror .image-wrapper {
  margin: 1.5rem 0;
  display: block;
  position: relative;
  background: #f9fafb;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  width: fit-content;
  max-width: 100%;
}

.ProseMirror .image-wrapper.align-left {
  align-items: flex-start;
  text-align: left;
  float: left;
  clear: left;
  margin: 0.5rem 1.5rem 1rem 0;
  max-width: 45%;
}

.ProseMirror .image-wrapper.align-center {
  align-items: center;
  text-align: center;
  clear: both;
  margin: 1.5rem auto;
  display: block;
}

.ProseMirror .image-wrapper.align-right {
  align-items: flex-end;
  text-align: right;
  float: right;
  clear: right;
  margin: 0.5rem 0 1rem 1.5rem;
  max-width: 45%;
}

.ProseMirror .custom-image {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  display: block;
  margin: 0;
  border: 2px solid #ffffff;
  /* Let the image define the container width */
  width: auto;
  min-width: 0;
}

.ProseMirror .custom-image:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Text wrapping improvements */
.ProseMirror p {
  overflow: hidden;
}

/* Clear floats after floating images */
.ProseMirror .image-wrapper.align-left + p,
.ProseMirror .image-wrapper.align-right + p {
  text-align: justify;
  line-height: 1.8;
}

/* Enhanced text wrapping for images */
.ProseMirror p + .image-wrapper.align-left,
.ProseMirror p + .image-wrapper.align-right {
  margin-top: 0.5rem;
}

.ProseMirror .image-wrapper.align-left + p,
.ProseMirror .image-wrapper.align-right + p {
  margin-top: 0.5rem;
  text-indent: 0;
  hyphens: auto;
  word-break: break-word;
}

/* Better paragraph spacing around floating images */
.ProseMirror .image-wrapper.align-left ~ p,
.ProseMirror .image-wrapper.align-right ~ p {
  text-align: justify;
  line-height: 1.8;
}

/* Prevent orphaned text next to images */
.ProseMirror .image-wrapper.align-left + p:first-line,
.ProseMirror .image-wrapper.align-right + p:first-line {
  min-height: 2em;
}

/* Style for captions if needed */
.ProseMirror .image-wrapper .image-caption {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  font-style: italic;
  margin-top: 0.5rem;
  padding: 0 0.5rem;
}

/* Image layout styles for different presets */
.ProseMirror .image-wrapper .custom-image {
  object-fit: cover;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  /* Use the inline width/height styles set by the toolbar */
  width: inherit;
  height: inherit;
}

/* Responsive layout - container adjusts to image size */
.ProseMirror .image-wrapper .custom-image[style*="width: 100%"] {
  width: 100% !important;
  height: auto;
}

.ProseMirror .image-wrapper .custom-image[style*="width: 100%"] ~ * {
  width: 100%;
}

/* For images with specific pixel dimensions */
.ProseMirror .image-wrapper .custom-image[style*="300px"] {
  width: 300px;
}

.ProseMirror .image-wrapper .custom-image[style*="500px"] {
  width: 500px;
}

.ProseMirror .image-wrapper .custom-image[style*="800px"] {
  width: 800px;
}

.ProseMirror .image-wrapper .custom-image[style*="400px"][style*="400px"] {
  width: 400px;
  height: 400px;
  object-fit: cover;
  object-position: center;
}

.ProseMirror .image-wrapper .custom-image[style*="600px"][style*="300px"] {
  width: 600px;
  height: 300px;
  object-fit: cover;
  object-position: center;
}

.ProseMirror .image-wrapper .custom-image[style*="400px"][style*="600px"] {
  width: 400px;
  height: 600px;
  object-fit: cover;
  object-position: center;
}

/* Banner style layout */
.ProseMirror .image-wrapper .custom-image[style*="height: 250px"] {
  width: 100%;
  height: 250px;
  object-fit: cover;
  object-position: center;
}

/* Hover effects for layout previews */
.ProseMirror .image-wrapper:hover .custom-image {
  transform: scale(1.02);
}

@media (max-width: 768px) {
  .ProseMirror .image-wrapper .custom-image {
    transform: none !important;
  }
}

/* Center aligned images get more prominence */
.ProseMirror .image-wrapper.align-center {
  background: linear-gradient(145deg, #f3f4f6 0%, #ffffff 100%);
  border: 1px solid #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Responsive image sizing */
@media (max-width: 768px) {
  .ProseMirror .custom-image {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  .ProseMirror .image-wrapper {
    margin: 1.5rem 0;
    padding: 0.75rem;
  }
}

/* Image selection state */
.ProseMirror .image-wrapper img.ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

.ProseMirror mark {
  background-color: #fef08a;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Article Content Styles (for public pages) */
.article-content {
  line-height: 1.7;
}

.article-content h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 2rem 0 1rem 0;
  line-height: 1.2;
  color: #1f2937;
}

.article-content h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1.5rem 0 0.75rem 0;
  line-height: 1.3;
  color: #374151;
}

.article-content h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1.25rem 0 0.5rem 0;
  line-height: 1.4;
  color: #4b5563;
}

.article-content p {
  margin: 1rem 0;
  color: #374151;
}

.article-content ul, .article-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.article-content li {
  margin: 0.5rem 0;
}

.article-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #6b7280;
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 0.5rem;
}

.article-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #dc2626;
}

.article-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.article-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.article-content table {
  border-collapse: collapse;
  margin: 1.5rem 0;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.article-content th, .article-content td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  text-align: left;
}

.article-content th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.article-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.article-content a {
  color: #3b82f6;
  text-decoration: underline;
  font-weight: 500;
}

.article-content a:hover {
  color: #1d4ed8;
}

.article-content mark {
  background-color: #fef08a;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Image Alignment Styles */
.image-wrapper {
  margin: 1.5rem 0;
  display: flex;
  position: relative;
}

.image-wrapper.align-left {
  justify-content: flex-start;
  margin-right: 1rem;
  float: left;
  clear: left;
  max-width: 50%;
}

.image-wrapper.align-center {
  justify-content: center;
  clear: both;
  max-width: 100%;
}

.image-wrapper.align-right {
  justify-content: flex-end;
  margin-left: 1rem;
  float: right;
  clear: right;
  max-width: 50%;
}

/* Custom Image Editor Styles */
.custom-image {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: block;
  user-select: none;
  -webkit-user-drag: none;
}

.image-selected .custom-image {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Image wrapper with resize capability */
.ProseMirror .image-wrapper {
  margin: 1rem 0;
  position: relative;
  display: block;
  width: fit-content;
  max-width: 100%;
}

.ProseMirror .image-wrapper[data-image-wrapper="true"] {
  position: relative;
}

.ProseMirror .image-wrapper[data-image-wrapper="true"]:hover {
  outline: 1px dashed #3b82f6;
  outline-offset: 2px;
}

.ProseMirror .image-wrapper.align-left {
  float: left;
  clear: left;
  margin: 0.5rem 1.5rem 1rem 0;
  max-width: 45%;
}

.ProseMirror .image-wrapper.align-center {
  clear: both;
  display: block;
  margin: 1.5rem auto;
  text-align: center;
}

.ProseMirror .image-wrapper.align-right {
  float: right;
  clear: right;
  margin: 0.5rem 0 1rem 1.5rem;
  max-width: 45%;
}

.ProseMirror .custom-image {
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 100%;
  height: auto;
}

.ProseMirror .custom-image:hover {
  transform: scale(1.01);
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
}

/* Prevent drag and drop while resizing */
.image-resizing {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.image-resizing * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Resize handles styling */
.image-resize-handle {
  position: absolute;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 2px;
  width: 12px;
  height: 12px;
  z-index: 1000;
}

.image-resize-handle:hover {
  background: #2563eb;
  transform: scale(1.1);
}

/* Resize cursors */
.cursor-nw-resize { cursor: nw-resize; }
.cursor-ne-resize { cursor: ne-resize; }
.cursor-sw-resize { cursor: sw-resize; }
.cursor-se-resize { cursor: se-resize; }
.cursor-n-resize { cursor: n-resize; }
.cursor-s-resize { cursor: s-resize; }
.cursor-w-resize { cursor: w-resize; }
.cursor-e-resize { cursor: e-resize; }

/* Image selection outline */
.image-selected {
  outline-offset: 4px;
}

/* Prevent text selection during resize */
.image-resizing {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.image-resizing * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Responsive image alignment */
@media (max-width: 768px) {
  .ProseMirror .image-wrapper {
    float: none !important;
    clear: both !important;
    width: 100% !important;
    max-width: 100% !important;
    display: block !important;
    margin: 1.5rem 0 !important;
    shape-outside: none !important;
  }

  .ProseMirror .image-wrapper.align-left,
  .ProseMirror .image-wrapper.align-right,
  .ProseMirror .image-wrapper.align-center {
    float: none !important;
    clear: both !important;
    width: 100% !important;
    max-width: 100% !important;
    display: block !important;
    margin: 1.5rem 0 !important;
    shape-outside: none !important;
  }

  .ProseMirror .image-wrapper .custom-image {
    max-width: 100%;
    width: auto !important;
    height: auto !important;
  }

  .image-wrapper.align-left,
  .image-wrapper.align-right {
    float: none;
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
    justify-content: center;
  }

  /* Disable text wrapping on mobile for better readability */
  .ProseMirror p {
    clear: both;
  }
}

/* GuttenBerg Editor Styles */
.image-container {
  position: relative;
  display: block;
  margin: 15px 0;
  clear: both;
  z-index: 1;
}

.image-container img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
  border-radius: 4px;
  transition: border-color 0.2s ease;
}

.image-container img:hover {
  border-color: #3b82f6 !important;
}

/* Prevent image containers from interfering with form layout */
[contenteditable] .image-container {
  position: relative;
  z-index: 1;
}

/* Editor focus styles */
[contenteditable]:focus {
  outline: none;
}

/* Ensure proper containment */
[contenteditable] {
  position: relative;
  overflow: hidden;
}

/* Drag & Drop Styles */
.image-container.dragging {
  opacity: 0.5;
  transform: scale(0.95);
  transition: all 0.2s ease;
}

.image-container:hover .drag-handle {
  opacity: 1 !important;
}

.drop-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3b82f6;
  z-index: 20;
  transition: top 0.1s ease;
}

.drop-indicator::before {
  content: '';
  position: absolute;
  left: -4px;
  top: -2px;
  width: 8px;
  height: 6px;
  background-color: #3b82f6;
  border-radius: 50%;
}

.drop-indicator::after {
  content: '';
  position: absolute;
  right: -4px;
  top: -2px;
  width: 8px;
  height: 6px;
  background-color: #3b82f6;
  border-radius: 50%;
}

/* Drag handle styles */
.drag-handle {
  position: absolute;
  top: 5px;
  left: 5px;
  background: rgba(59, 130, 246, 0.9);
  color: white;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 12px;
  cursor: move;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 10;
  user-select: none;
  font-family: monospace;
  line-height: 1;
}

.drag-handle:hover {
  background: rgba(59, 130, 246, 1);
}

/* Dragging state */
.image-container[draggable="true"]:hover {
  cursor: move;
}

.image-container[draggable="true"]:hover img {
  pointer-events: auto;
}
