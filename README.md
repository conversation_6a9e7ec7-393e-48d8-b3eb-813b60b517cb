# CMS Application

A modern Content Management System built with Next.js 15, MySQL, Drizzle ORM, and ShadCN UI.

## Features

- 🔐 **Authentication System** - Secure login/logout with NextAuth.js
- 📝 **Article Management** - Full CRUD operations for articles
- 🎨 **Rich Text Editor** - Create and edit content with ease
- 🖼️ **Image Upload** - Upload and manage cover images
- 📱 **Responsive Design** - Works on all devices
- 🎠 **Homepage Marquee** - Featured articles carousel
- 🔍 **Search Functionality** - Find articles quickly
- 📊 **Admin Dashboard** - Comprehensive content management
- 🎯 **SEO Friendly** - Optimized for search engines

## Tech Stack

- **Frontend**: Next.js 15 (App Router), TypeScript, Tailwind CSS
- **UI Components**: ShadCN UI
- **Database**: MySQL with Drizzle ORM
- **Authentication**: NextAuth.js
- **Animations**: react-fast-marquee
- **Icons**: Lucide React

## Getting Started

### Prerequisites

- Node.js 18+
- MySQL database
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cms
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your database credentials:
   ```env
   DATABASE_URL="mysql://username:password@localhost:3306/cms_db"
   NEXTAUTH_SECRET="your-secret-key-here"
   NEXTAUTH_URL="http://localhost:3000"
   ADMIN_EMAIL="<EMAIL>"
   ADMIN_PASSWORD="admin123"
   ```

4. **Setup the database**
   ```bash
   # Generate migration files
   npm run db:generate

   # Push schema to database
   npm run db:push
   ```

5. **Initialize admin user**
   ```bash
   # Start the development server first
   npm run dev

   # Then in another terminal, create the admin user
   curl -X POST http://localhost:3000/api/init
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Usage

### Admin Access

1. Go to `/login` and sign in with your admin credentials
2. Access the dashboard at `/dashboard`
3. Create, edit, and manage articles
4. Upload images for article covers

### Public Features

- **Homepage** (`/`): Features a marquee of latest articles
- **Articles** (`/artikel`): Browse all published articles
- **Article Detail** (`/artikel/[slug]`): Read individual articles

## Project Structure

```
├── app/
│   ├── (admin)/dashboard/     # Admin dashboard pages
│   ├── (auth)/login/          # Authentication pages
│   ├── (public)/              # Public pages
│   └── api/                   # API routes
├── components/
│   ├── admin/                 # Admin-specific components
│   ├── public/                # Public-facing components
│   └── ui/                    # Reusable UI components
├── db/
│   ├── schema.ts              # Database schema
│   └── index.ts               # Database connection
├── lib/                       # Utility functions
└── types/                     # TypeScript type definitions
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate database migrations
- `npm run db:migrate` - Run database migrations
- `npm run db:push` - Push schema to database
- `npm run db:studio` - Open Drizzle Studio

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
