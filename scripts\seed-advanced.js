#!/usr/bin/env node

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const { createId } = require('@paralleldrive/cuid2');

// Extended sample data with more articles
const sampleUsers = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'editor123',
    name: 'Editor User',
    role: 'user'
  },
  {
    email: '<EMAIL>',
    password: 'writer123',
    name: 'Content Writer',
    role: 'user'
  }
];

const articleTemplates = [
  {
    title: 'Getting Started with {tech}',
    content: `# Introduction to {tech}

{tech} is a powerful technology that has revolutionized the way we build applications. In this comprehensive guide, we'll explore the fundamentals and help you get started.

## What is {tech}?

{tech} provides developers with the tools and capabilities needed to create modern, scalable applications. Its key features include:

- **Performance**: Optimized for speed and efficiency
- **Developer Experience**: Intuitive APIs and excellent tooling
- **Scalability**: Built to handle applications of any size
- **Community**: Strong ecosystem and community support

## Getting Started

### Installation

First, you'll need to install {tech}:

\`\`\`bash
npm install {tech}
\`\`\`

### Basic Setup

Create a new project and configure {tech}:

\`\`\`javascript
import { {tech} } from '{tech}';

const app = new {tech}({
  // Configuration options
});
\`\`\`

## Best Practices

1. **Follow conventions**: Stick to established patterns
2. **Write tests**: Ensure code quality and reliability
3. **Document your code**: Make it maintainable
4. **Optimize performance**: Profile and improve bottlenecks
5. **Stay updated**: Keep up with the latest versions

## Conclusion

{tech} is an excellent choice for modern development. With its powerful features and active community, you'll be building amazing applications in no time!`,
    excerpt: 'A comprehensive guide to getting started with {tech}, covering installation, setup, and best practices.',
    topics: ['React', 'Vue.js', 'Angular', 'Svelte', 'Next.js', 'Nuxt.js', 'TypeScript', 'Node.js']
  },
  {
    title: 'Advanced {concept} Techniques',
    content: `# Mastering {concept}

{concept} is a crucial skill for any developer. This advanced guide will take your understanding to the next level.

## Core Principles

Understanding the fundamental principles of {concept} is essential:

### Principle 1: Clarity
Your {concept} should be clear and easy to understand. Avoid unnecessary complexity and focus on solving the problem at hand.

### Principle 2: Efficiency
Optimize for performance without sacrificing readability. The best solutions are both fast and maintainable.

### Principle 3: Scalability
Design with growth in mind. Your {concept} should work well for small projects and scale to enterprise applications.

## Advanced Patterns

### Pattern 1: Composition
Break complex problems into smaller, reusable components:

\`\`\`javascript
function compose(f, g) {
  return function(x) {
    return f(g(x));
  };
}
\`\`\`

### Pattern 2: Abstraction
Hide implementation details behind clean interfaces:

\`\`\`javascript
class DataProcessor {
  process(data) {
    return this.transform(this.validate(data));
  }
  
  validate(data) {
    // Validation logic
  }
  
  transform(data) {
    // Transformation logic
  }
}
\`\`\`

## Real-World Applications

{concept} is used extensively in:

- **Web Development**: Building user interfaces and APIs
- **Data Processing**: Analyzing and transforming large datasets
- **System Design**: Creating scalable architectures
- **DevOps**: Automating deployment and monitoring

## Common Pitfalls

Avoid these common mistakes:

1. **Over-engineering**: Keep solutions simple
2. **Premature optimization**: Focus on correctness first
3. **Ignoring edge cases**: Test thoroughly
4. **Poor error handling**: Plan for failure scenarios

## Conclusion

Mastering {concept} takes time and practice. Start with the basics, gradually incorporate advanced techniques, and always keep learning!`,
    excerpt: 'Take your {concept} skills to the next level with advanced techniques, patterns, and real-world applications.',
    topics: ['Database Design', 'API Development', 'State Management', 'Performance Optimization', 'Security', 'Testing', 'DevOps', 'System Architecture']
  },
  {
    title: 'The Complete Guide to {tool}',
    content: `# Everything You Need to Know About {tool}

{tool} has become an essential part of the modern development workflow. This comprehensive guide covers everything from basics to advanced usage.

## Introduction

{tool} simplifies complex tasks and improves developer productivity. Whether you're a beginner or an experienced developer, this guide will help you make the most of {tool}.

## Installation and Setup

### System Requirements
- Operating System: Windows, macOS, or Linux
- Memory: 4GB RAM minimum, 8GB recommended
- Storage: 1GB available space

### Installation Steps

1. Download {tool} from the official website
2. Run the installer and follow the prompts
3. Verify installation: \`{tool} --version\`
4. Configure your environment

### Initial Configuration

\`\`\`bash
{tool} config --global user.name "Your Name"
{tool} config --global user.email "<EMAIL>"
\`\`\`

## Core Features

### Feature 1: Project Management
{tool} provides excellent project management capabilities:

- Create and organize projects
- Track dependencies and versions
- Manage build configurations
- Handle deployment pipelines

### Feature 2: Collaboration
Work effectively with your team:

- Version control integration
- Code review workflows
- Issue tracking
- Documentation generation

### Feature 3: Automation
Automate repetitive tasks:

- Build processes
- Testing workflows
- Deployment procedures
- Code quality checks

## Advanced Usage

### Custom Scripts
Create custom scripts for your workflow:

\`\`\`json
{
  "scripts": {
    "dev": "{tool} start --mode development",
    "build": "{tool} build --optimize",
    "test": "{tool} test --coverage",
    "deploy": "{tool} deploy --production"
  }
}
\`\`\`

### Plugins and Extensions
Extend {tool} with plugins:

- Syntax highlighting
- Code formatting
- Linting and validation
- Performance monitoring

## Troubleshooting

### Common Issues

**Issue**: {tool} command not found
**Solution**: Add {tool} to your PATH environment variable

**Issue**: Permission denied errors
**Solution**: Run with appropriate permissions or use sudo

**Issue**: Slow performance
**Solution**: Clear cache and update to latest version

### Getting Help

- Official documentation
- Community forums
- GitHub issues
- Stack Overflow

## Best Practices

1. **Keep it updated**: Use the latest stable version
2. **Follow conventions**: Stick to established patterns
3. **Use version control**: Track all changes
4. **Document your setup**: Make it reproducible
5. **Monitor performance**: Profile and optimize

## Conclusion

{tool} is a powerful addition to any developer's toolkit. With proper setup and understanding of its features, you'll significantly improve your development workflow.`,
    excerpt: 'A complete guide to {tool}, covering installation, configuration, features, and best practices for maximum productivity.',
    topics: ['Docker', 'Kubernetes', 'Git', 'Webpack', 'Vite', 'ESLint', 'Prettier', 'Jest']
  }
];

const unsplashImages = [
  'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=800&h=400&fit=crop',
  'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop'
];

function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
}

function generateArticles() {
  const articles = [];
  
  articleTemplates.forEach(template => {
    template.topics.forEach((topic, index) => {
      const title = template.title.replace(/{tech}|{concept}|{tool}/g, topic);
      const content = template.content.replace(/{tech}|{concept}|{tool}/g, topic);
      const excerpt = template.excerpt.replace(/{tech}|{concept}|{tool}/g, topic);
      
      articles.push({
        title,
        content,
        excerpt,
        coverImage: unsplashImages[Math.floor(Math.random() * unsplashImages.length)],
        status: Math.random() > 0.2 ? 'published' : 'draft' // 80% published, 20% draft
      });
    });
  });
  
  return articles;
}

async function seedAdvanced() {
  console.log('🌱 Starting advanced database seeding...');
  
  try {
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl) {
      throw new Error('DATABASE_URL environment variable is required');
    }
    
    const url = new URL(dbUrl);
    
    const connection = await mysql.createConnection({
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      user: url.username,
      password: url.password,
      database: url.pathname.slice(1),
    });
    
    console.log('✅ Connected to database');
    
    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await connection.execute('DELETE FROM articles');
    await connection.execute('DELETE FROM users');
    
    // Seed users
    console.log('👥 Seeding users...');
    const userIds = [];
    
    for (const user of sampleUsers) {
      const userId = createId();
      const hashedPassword = await bcrypt.hash(user.password, 12);
      
      await connection.execute(
        'INSERT INTO users (id, email, password, name, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
        [userId, user.email, hashedPassword, user.name, user.role]
      );
      
      userIds.push(userId);
      console.log(`   ✓ Created user: ${user.email}`);
    }
    
    // Generate and seed articles
    console.log('📝 Generating and seeding articles...');
    const articles = generateArticles();
    
    for (let i = 0; i < articles.length; i++) {
      const article = articles[i];
      const articleId = createId();
      const slug = generateSlug(article.title);
      const authorId = userIds[Math.floor(Math.random() * userIds.length)]; // Random author
      const publishedAt = article.status === 'published' ? new Date() : null;
      
      await connection.execute(
        'INSERT INTO articles (id, title, slug, content, excerpt, cover_image, status, author_id, published_at, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())',
        [articleId, article.title, slug, article.content, article.excerpt, article.coverImage, article.status, authorId, publishedAt]
      );
      
      console.log(`   ✓ Created article: ${article.title}`);
    }
    
    await connection.end();
    
    console.log('🎉 Advanced database seeding completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log(`   • ${sampleUsers.length} users created`);
    console.log(`   • ${articles.length} articles created`);
    console.log(`   • ${articles.filter(a => a.status === 'published').length} published articles`);
    console.log(`   • ${articles.filter(a => a.status === 'draft').length} draft articles`);
    console.log('');
    console.log('🔑 Login credentials:');
    console.log('   Admin: <EMAIL> / admin123');
    console.log('   Editor: <EMAIL> / editor123');
    console.log('   Writer: <EMAIL> / writer123');
    
  } catch (error) {
    console.error('❌ Advanced seeding failed:', error.message);
    process.exit(1);
  }
}

seedAdvanced();
