"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Marquee from "react-fast-marquee";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";

interface Article {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  coverImage: string;
  publishedAt: string;
}

export default function HomePage() {
  const [featuredArticles, setFeaturedArticles] = useState<Article[]>([]);
  const [recentArticles, setRecentArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchArticles = async () => {
      try {
        const response = await fetch("/api/articles?published=true&limit=10");
        const data = await response.json();
        
        if (data.articles) {
          // Take first 5 for marquee
          setFeaturedArticles(data.articles.slice(0, 5));
          // Take next 6 for recent articles section
          setRecentArticles(data.articles.slice(0, 6));
        }
      } catch (error) {
        console.error("Error fetching articles:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading articles...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-12">
      {/* Hero Section with Marquee */}
      <section className="py-12 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Welcome to Our Blog
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Discover amazing articles and insights from our content creators
            </p>
          </div>

          {/* Featured Articles Marquee */}
          {featuredArticles.length > 0 && (
            <div className="mt-12">
              <h2 className="text-2xl font-bold text-center mb-6">Featured Articles</h2>
              <Marquee pauseOnHover speed={50} gradient={false}>
                {featuredArticles.map((article) => (
                  <Link
                    key={article.id}
                    href={`/artikel/${article.slug}`}
                    className="mx-4 block"
                  >
                    <Card className="w-80 hover:shadow-lg transition-shadow">
                      <CardContent className="p-0">
                        {article.coverImage && (
                          <img
                            src={article.coverImage}
                            alt={article.title}
                            className="w-full h-48 object-cover rounded-t-lg"
                          />
                        )}
                        <div className="p-4">
                          <h3 className="font-semibold text-lg mb-2 line-clamp-2">
                            {article.title}
                          </h3>
                          <p className="text-sm text-muted-foreground line-clamp-3">
                            {article.excerpt}
                          </p>
                          <p className="text-xs text-muted-foreground mt-2">
                            {formatDate(article.publishedAt)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </Marquee>
            </div>
          )}
        </div>
      </section>

      {/* Recent Articles Section */}
      <section className="py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold tracking-tight">Recent Articles</h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Stay up to date with our latest content
            </p>
          </div>

          {recentArticles.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {recentArticles.map((article) => (
                <Link key={article.id} href={`/artikel/${article.slug}`}>
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardContent className="p-0">
                      {article.coverImage && (
                        <img
                          src={article.coverImage}
                          alt={article.title}
                          className="w-full h-48 object-cover rounded-t-lg"
                        />
                      )}
                      <div className="p-6">
                        <h3 className="font-semibold text-xl mb-2 line-clamp-2">
                          {article.title}
                        </h3>
                        <p className="text-muted-foreground line-clamp-3 mb-4">
                          {article.excerpt}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(article.publishedAt)}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No articles published yet.</p>
            </div>
          )}

          <div className="text-center mt-8">
            <Link href="/artikel">
              <Button size="lg">
                View All Articles
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
