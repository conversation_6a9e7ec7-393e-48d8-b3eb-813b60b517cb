"use client";

import { useState } from 'react';
import { SimpleImageResizer } from './simple-image-resizer';

export function ImageResizeTest() {
  const [selectedImage, setSelectedImage] = useState<HTMLImageElement | null>(null);

  const handleImageClick = (e: React.MouseEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    setSelectedImage(img);
  };

  const handleResize = (width: number, height: number) => {
    console.log('Image resized to:', { width, height });
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Image Resize Test</h2>
      
      <div className="space-y-4">
        <img
          src="/uploads/test-image.jpg"
          alt="Test Image 1"
          className="cursor-pointer border-2 border-gray-300 hover:border-blue-500 transition-colors"
          onClick={handleImageClick}
          style={{ width: '300px', height: 'auto' }}
        />
        
        <img
          src="/next.svg"
          alt="Test Image 2"
          className="cursor-pointer border-2 border-gray-300 hover:border-blue-500 transition-colors"
          onClick={handleImageClick}
          style={{ width: '200px', height: 'auto' }}
        />
      </div>

      {selectedImage && (
        <SimpleImageResizer
          imageElement={selectedImage}
          onResize={handleResize}
        />
      )}

      <div className="mt-4 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold">Instructions:</h3>
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>Click on any image to select it</li>
          <li>Drag the blue resize handle at the bottom-right corner</li>
          <li>The image will resize proportionally</li>
          <li>Check the console for resize events</li>
        </ul>
      </div>
    </div>
  );
}
