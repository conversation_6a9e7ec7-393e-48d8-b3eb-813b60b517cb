"use client";

import { useEffect, useState } from "react";
import { ArticleForm } from "@/components/admin/article-form";
import { LoadingPage } from "@/components/ui/loading";
import { toast } from "sonner";

interface Article {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  coverImage: string;
  status: "draft" | "published";
}

export default function EditArticlePage({ params }: { params: { id: string } }) {
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        const response = await fetch(`/api/articles/${params.id}`);
        if (response.ok) {
          const data = await response.json();
          setArticle(data);
        } else {
          toast.error("Article not found");
        }
      } catch (error) {
        console.error("Error fetching article:", error);
        toast.error("Failed to fetch article");
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [params.id]);

  if (loading) {
    return <LoadingPage />;
  }

  if (!article) {
    return (
      <div className="text-center py-8">
        <h1 className="text-2xl font-bold">Article not found</h1>
        <p className="text-muted-foreground">The article you're looking for doesn't exist.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Edit Article</h1>
        <p className="text-muted-foreground">
          Update your article content and settings
        </p>
      </div>
      
      <ArticleForm article={article} isEditing={true} />
    </div>
  );
}
