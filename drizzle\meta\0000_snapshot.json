{"version": "5", "dialect": "mysql", "id": "183c39db-e41b-4a6d-9da2-887db1bc19bc", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"articles": {"name": "articles", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_image": {"name": "cover_image", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "enum('draft','published')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'draft'"}, "author_id": {"name": "author_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"articles_id": {"name": "articles_id", "columns": ["id"]}}, "uniqueConstraints": {"articles_slug_unique": {"name": "articles_slug_unique", "columns": ["slug"]}}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "enum('admin','user')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'user'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}