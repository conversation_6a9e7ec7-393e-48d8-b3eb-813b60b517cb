"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Eye, Users, TrendingUp } from "lucide-react";

interface DashboardStats {
  totalArticles: number;
  publishedArticles: number;
  draftArticles: number;
  totalViews: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalArticles: 0,
    publishedArticles: 0,
    draftArticles: 0,
    totalViews: 0,
  });

  useEffect(() => {
    // Fetch dashboard stats
    const fetchStats = async () => {
      try {
        const response = await fetch("/api/articles");
        const data = await response.json();
        
        if (data.articles) {
          const total = data.articles.length;
          const published = data.articles.filter((a: any) => a.status === "published").length;
          const draft = data.articles.filter((a: any) => a.status === "draft").length;
          
          setStats({
            totalArticles: total,
            publishedArticles: published,
            draftArticles: draft,
            totalViews: Math.floor(Math.random() * 10000), // Mock data
          });
        }
      } catch (error) {
        console.error("Error fetching stats:", error);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: "Total Articles",
      value: stats.totalArticles,
      description: "All articles in the system",
      icon: FileText,
    },
    {
      title: "Published",
      value: stats.publishedArticles,
      description: "Live articles",
      icon: Eye,
    },
    {
      title: "Drafts",
      value: stats.draftArticles,
      description: "Unpublished articles",
      icon: Users,
    },
    {
      title: "Total Views",
      value: stats.totalViews,
      description: "Article views this month",
      icon: TrendingUp,
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to your content management system
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((card) => (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="ml-4 space-y-1">
                  <p className="text-sm font-medium leading-none">
                    Welcome to your CMS dashboard
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Start by creating your first article
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks you might want to perform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <a 
                href="/dashboard/articles/new" 
                className="block p-2 rounded hover:bg-muted transition-colors"
              >
                Create New Article
              </a>
              <a 
                href="/dashboard/articles" 
                className="block p-2 rounded hover:bg-muted transition-colors"
              >
                Manage Articles
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
