# Text Wrapping dengan Gambar - Panduan Penggunaan

## ✨ Fitur Baru: Text Sejajar dengan Gambar

Sekarang editor mendukung **text wrapping** di sekitar gambar, mirip seperti pada majalah atau artikel profesional.

## 🎯 Cara Menggunakan

### 1. **Left Alignment (<PERSON><PERSON><PERSON>, <PERSON> Kanan)**
- <PERSON><PERSON><PERSON> gambar → Klik tombol align left di image toolbar
- Text akan mengalir di sebelah kanan gambar
- Ideal untuk: G<PERSON>bar potret, thumbnail, ilustrasi pendukung

### 2. **Right Alignment (Gambar Kanan, Text Kiri)**  
- <PERSON><PERSON>h gambar → Klik tombol align right di image toolbar
- Text akan mengalir di sebelah kiri gambar
- Ideal untuk: G<PERSON>bar penjelasan, diagram, chart

### 3. **Center Alignment (Gambar di Tengah)**
- <PERSON><PERSON>h gambar → Klik tombol align center di image toolbar
- Gambar berada di tengah, text terpisah di atas dan bawah
- Ideal untuk: <PERSON><PERSON><PERSON> utama, hero image, gambar penting

## 🔧 Fitur Teknis

### Responsive Design
- **Desktop**: Text mengalir di samping gambar (45% max width untuk gambar)
- **Mobile**: Gambar menjadi full width, text di atas/bawah untuk readability

### Auto-sizing
- Container gambar otomatis menyesuaikan ukuran gambar
- Text spacing dan margin otomatis disesuaikan
- Shape-outside CSS untuk text wrapping yang halus

### Typography Enhancement
- Text justify alignment untuk tampilan profesional
- Line-height optimal (1.8) untuk readability
- Hyphenation otomatis untuk text yang rapi
- Proper spacing antara paragraf dan gambar

## 📐 Size Guidelines

### Ukuran Gambar untuk Text Wrapping
- **Small (300px)**: Ideal untuk text wrapping, banyak ruang untuk text
- **Medium (500px)**: Balance antara gambar dan text
- **Large (800px)**: Lebih cocok untuk center alignment

### Layout Tips
1. **Left/Right align**: Gunakan gambar small-medium (300-500px)
2. **Center align**: Gunakan gambar medium-large (500-800px)  
3. **Responsive**: Gunakan untuk gambar full-width

## 🎨 Visual Examples

```
┌─────────────────────────────────────┐
│ LEFT ALIGNMENT                      │
│ ┌────────┐ Lorem ipsum dolor sit    │
│ │        │ amet, consectetur        │
│ │ IMAGE  │ adipiscing elit, sed do  │
│ │        │ eiusmod tempor incididunt│
│ └────────┘ ut labore et dolore      │
│           magna aliqua. Ut enim     │
│           ad minim veniam...        │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ RIGHT ALIGNMENT                     │
│   Lorem ipsum dolor sit ┌────────┐  │
│   amet, consectetur     │        │  │
│   adipiscing elit, sed  │ IMAGE  │  │
│   do eiusmod tempor     │        │  │
│   incididunt ut labore  └────────┘  │
│   et dolore magna aliqua...         │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ CENTER ALIGNMENT                    │
│          ┌──────────────┐           │
│          │    IMAGE     │           │
│          └──────────────┘           │
│  Lorem ipsum dolor sit amet,        │
│  consectetur adipiscing elit...     │
└─────────────────────────────────────┘
```

## 🚀 Manfaat

1. **Professional Layout**: Tampilan seperti majalah/artikel profesional
2. **Space Efficiency**: Memaksimalkan penggunaan ruang halaman
3. **Better Readability**: Text flow yang natural dan mudah dibaca
4. **Responsive**: Otomatis beradaptasi dengan ukuran layar
5. **User Friendly**: Interface yang intuitif dan mudah digunakan

## 💡 Best Practices

1. **Gunakan gambar kecil-sedang** untuk left/right alignment
2. **Pisahkan gambar besar** dengan center alignment
3. **Konsisten dengan alignment** dalam satu artikel
4. **Test di mobile** untuk memastikan readability
5. **Pertimbangkan aspect ratio** gambar untuk text wrapping

**Status: ✅ READY TO USE - Text Wrapping Feature Complete!**
