import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db, articles } from "@/db";
import { eq, desc, like, and } from "drizzle-orm";
import { generateSlug } from "@/lib/utils";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "10");
  const search = searchParams.get("search") || "";
  const status = searchParams.get("status") || "";
  const published = searchParams.get("published") === "true";

  const offset = (page - 1) * limit;

  try {
    let query = db.select().from(articles);
    
    const conditions = [];
    
    if (search) {
      conditions.push(like(articles.title, `%${search}%`));
    }
    
    if (status) {
      conditions.push(eq(articles.status, status as "draft" | "published"));
    }
    
    if (published) {
      conditions.push(eq(articles.status, "published"));
    }
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    const result = await query
      .orderBy(desc(articles.createdAt))
      .limit(limit)
      .offset(offset);

    return NextResponse.json({
      articles: result,
      pagination: {
        page,
        limit,
        total: result.length, // In a real app, you'd want to count total separately
      }
    });
  } catch (error) {
    console.error("Error fetching articles:", error);
    return NextResponse.json(
      { error: "Failed to fetch articles" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { title, content, excerpt, coverImage, status } = body;

    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      );
    }

    const slug = generateSlug(title);
    
    // Check if slug already exists
    const existingArticle = await db
      .select()
      .from(articles)
      .where(eq(articles.slug, slug))
      .limit(1);

    if (existingArticle.length > 0) {
      return NextResponse.json(
        { error: "Article with this title already exists" },
        { status: 400 }
      );
    }

    const newArticle = await db.insert(articles).values({
      title,
      slug,
      content,
      excerpt,
      coverImage,
      status: status || "draft",
      authorId: session.user.id,
      publishedAt: status === "published" ? new Date() : null,
    });

    return NextResponse.json(
      { message: "Article created successfully", id: newArticle.insertId },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating article:", error);
    return NextResponse.json(
      { error: "Failed to create article" },
      { status: 500 }
    );
  }
}
