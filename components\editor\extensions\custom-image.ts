import { Node, mergeAttributes } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';

export interface ImageOptions {
  inline: boolean;
  allowBase64: boolean;
  HTMLAttributes: Record<string, any>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    customImage: {
      /**
       * Add an image
       */
      setImage: (options: { src: string; alt?: string; title?: string; align?: 'left' | 'center' | 'right'; width?: string; height?: string }) => ReturnType;
      /**
       * Update image alignment
       */
      updateImageAlign: (align: 'left' | 'center' | 'right') => ReturnType;
      /**
       * Update image size
       */
      updateImageSize: (width?: string, height?: string) => ReturnType;
      /**
       * Update image attributes
       */
      updateImageAttrs: (attrs: Partial<{ src: string; alt?: string; title?: string; align?: 'left' | 'center' | 'right'; width?: string; height?: string }>) => ReturnType;
    };
  }
}

export const CustomImage = Node.create<ImageOptions>({
  name: 'customImage',

  addOptions() {
    return {
      inline: false,
      allowBase64: false,
      HTMLAttributes: {},
    };
  },

  inline() {
    return this.options.inline;
  },

  group() {
    return this.options.inline ? 'inline' : 'block';
  },

  draggable: true,

  addAttributes() {
    return {
      src: {
        default: null,
      },
      alt: {
        default: null,
      },
      title: {
        default: null,
      },
      align: {
        default: 'center',
        renderHTML: attributes => {
          if (!attributes.align) {
            return {};
          }
          return {
            'data-align': attributes.align,
          };
        },
        parseHTML: element => element.getAttribute('data-align'),
      },
      width: {
        default: null,
        renderHTML: attributes => {
          if (!attributes.width) {
            return {};
          }
          return {
            width: attributes.width,
          };
        },
        parseHTML: element => element.getAttribute('width'),
      },
      height: {
        default: null,
        renderHTML: attributes => {
          if (!attributes.height) {
            return {};
          }
          return {
            height: attributes.height,
          };
        },
        parseHTML: element => element.getAttribute('height'),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'img[src]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    const { align, ...otherAttributes } = HTMLAttributes;
    
    let className = 'custom-image';
    if (align) {
      className += ` image-${align}`;
    }

    return [
      'div',
      { class: `image-wrapper ${align ? `align-${align}` : 'align-center'}` },
      [
        'img',
        mergeAttributes(this.options.HTMLAttributes, otherAttributes, {
          class: className,
        }),
      ],
    ];
  },

  addCommands() {
    return {
      setImage:
        options =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },

      updateImageAlign:
        align =>
        ({ tr, state, dispatch }) => {
          const { selection } = state;
          const { from } = selection;
          
          const node = state.doc.nodeAt(from);
          if (node && node.type.name === this.name) {
            if (dispatch) {
              tr.setNodeMarkup(from, undefined, {
                ...node.attrs,
                align,
              });
            }
            return true;
          }
          return false;
        },

      updateImageSize:
        (width, height) =>
        ({ tr, state, dispatch }) => {
          const { selection } = state;
          const { from } = selection;

          const node = state.doc.nodeAt(from);
          if (node && node.type.name === this.name) {
            if (dispatch) {
              tr.setNodeMarkup(from, undefined, {
                ...node.attrs,
                width,
                height,
              });
            }
            return true;
          }
          return false;
        },

      updateImageAttrs:
        (attrs) =>
        ({ tr, state, dispatch }) => {
          const { selection } = state;
          let found = false;

          // Find the image node in the current selection or around it
          state.doc.nodesBetween(selection.from, selection.to, (node, pos) => {
            if (node.type.name === this.name && !found) {
              if (dispatch) {
                tr.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  ...attrs,
                });
              }
              found = true;
              return false;
            }
          });

          return found;
        },
    };
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('imageSelection'),
        props: {
          decorations: (state) => {
            const { doc, selection } = state;
            const decorations: Decoration[] = [];

            // Add selection decoration for images
            if (selection.empty) {
              const node = doc.nodeAt(selection.from);
              if (node && node.type.name === this.name) {
                decorations.push(
                  Decoration.node(selection.from, selection.from + node.nodeSize, {
                    class: 'image-selected',
                  })
                );
              }
            }

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },
});
